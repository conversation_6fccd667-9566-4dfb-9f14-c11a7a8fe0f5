"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, Users, Award } from "lucide-react";

const highlights = [
  {
    icon: Star,
    value: "4.9/5",
    label: "Average Rating",
    color: "text-yellow-600"
  },
  {
    icon: Users,
    value: "50+",
    label: "Happy Clients",
    color: "text-blue-600"
  },
  {
    icon: Award,
    value: "98%",
    label: "Satisfaction Rate",
    color: "text-green-600"
  }
];

export function TestimonialsHero() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Badge variant="outline" className="mb-4 text-blue-600 border-blue-600">
              Client Testimonials
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight mb-6">
              What Our Clients{" "}
              <span className="gradient-text">Say About Us</span>
            </h1>
            
            <div className="flex items-center justify-center mb-6">
              <Quote className="h-12 w-12 text-blue-600 mr-4" />
              <p className="text-xl text-gray-600 max-w-3xl leading-relaxed">
                Don't just take our word for it. Here's what businesses across Nepal 
                say about working with DigitalBoost and the results we've achieved together.
              </p>
            </div>

            {/* Rating Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {highlights.map((highlight, index) => (
                <motion.div
                  key={highlight.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white p-6 rounded-xl shadow-lg"
                >
                  <highlight.icon className={`h-8 w-8 ${highlight.color} mx-auto mb-3`} />
                  <div className={`text-3xl font-bold ${highlight.color} mb-2`}>
                    {highlight.value}
                  </div>
                  <div className="text-gray-600">{highlight.label}</div>
                </motion.div>
              ))}
            </div>

            {/* Featured Quote */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto"
            >
              <div className="flex items-center justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-6 w-6 text-yellow-400 fill-current" />
                ))}
              </div>
              
              <blockquote className="text-xl text-gray-700 italic mb-6">
                "DigitalBoost didn't just help us with marketing – they transformed our entire 
                approach to customer engagement. Our business has grown beyond our wildest dreams!"
              </blockquote>
              
              <div className="flex items-center justify-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="text-left">
                  <div className="font-bold text-gray-900">Rajesh Sharma</div>
                  <div className="text-gray-600">Owner, Sharma Electronics</div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
