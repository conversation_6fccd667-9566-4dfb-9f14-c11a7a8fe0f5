{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/about-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Users, Target, Heart, Award } from \"lucide-react\";\n\nexport function AboutHero() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-50 to-purple-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-8\"\n          >\n            <Badge variant=\"outline\" className=\"text-blue-600 border-blue-600\">\n              About DigitalBoost\n            </Badge>\n            \n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 leading-tight\">\n              Empowering Businesses in{\" \"}\n              <span className=\"gradient-text\">Nepal</span>\n            </h1>\n            \n            <p className=\"text-xl text-gray-600 leading-relaxed\">\n              Founded in 2019 in the heart of Biratnagar, we've been on a mission to transform \n              how Nepalese businesses connect with their customers through innovative digital marketing strategies.\n            </p>\n            \n            <p className=\"text-lg text-gray-600 leading-relaxed\">\n              From small local shops to growing enterprises, we've helped over 50 businesses \n              across Nepal build their digital presence, increase their revenue, and create \n              lasting relationships with their customers.\n            </p>\n\n            {/* Quick Stats */}\n            <div className=\"grid grid-cols-2 gap-6 pt-8\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600\">5+</div>\n                <div className=\"text-gray-600\">Years Experience</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600\">50+</div>\n                <div className=\"text-gray-600\">Happy Clients</div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Right Content - Values Grid */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"grid grid-cols-2 gap-6\"\n          >\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg text-center\"\n            >\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Users className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <h3 className=\"font-bold text-gray-900 mb-2\">Client-Focused</h3>\n              <p className=\"text-sm text-gray-600\">Your success is our priority</p>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg text-center\"\n            >\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Target className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <h3 className=\"font-bold text-gray-900 mb-2\">Results-Driven</h3>\n              <p className=\"text-sm text-gray-600\">Measurable outcomes always</p>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg text-center\"\n            >\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Heart className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <h3 className=\"font-bold text-gray-900 mb-2\">Passionate</h3>\n              <p className=\"text-sm text-gray-600\">We love what we do</p>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-6 rounded-xl shadow-lg text-center\"\n            >\n              <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <h3 className=\"font-bold text-gray-900 mb-2\">Excellence</h3>\n              <p className=\"text-sm text-gray-600\">Quality in everything</p>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAgC;;;;;;0CAInE,8OAAC;gCAAG,WAAU;;oCAA6D;oCAChD;kDACzB,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAGlC,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAKrD,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAOrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/mission-vision.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Lightbulb, Rocket, Globe, Zap } from \"lucide-react\";\n\nexport function MissionVision() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-purple-600 border-purple-600\">\n            Our Purpose\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Mission & Vision\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Driving digital transformation across Nepal, one business at a time.\n          </p>\n        </motion.div>\n\n        {/* Mission & Vision Cards */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\">\n          {/* Mission */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"h-full border-0 shadow-lg\">\n              <CardContent className=\"p-8\">\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\">\n                    <Rocket className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-gray-900\">Our Mission</h3>\n                </div>\n                <p className=\"text-gray-600 leading-relaxed mb-6\">\n                  To empower Nepalese businesses with cutting-edge digital marketing solutions \n                  that drive growth, increase visibility, and create meaningful connections \n                  between brands and their customers.\n                </p>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span className=\"text-gray-700\">Democratize digital marketing for all business sizes</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span className=\"text-gray-700\">Deliver measurable results and ROI</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span className=\"text-gray-700\">Foster long-term partnerships</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Vision */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"h-full border-0 shadow-lg\">\n              <CardContent className=\"p-8\">\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4\">\n                    <Lightbulb className=\"h-6 w-6 text-purple-600\" />\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-gray-900\">Our Vision</h3>\n                </div>\n                <p className=\"text-gray-600 leading-relaxed mb-6\">\n                  To become Nepal's most trusted digital marketing agency, recognized for \n                  innovation, excellence, and our contribution to the digital transformation \n                  of Nepalese businesses.\n                </p>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                    <span className=\"text-gray-700\">Lead digital innovation in Nepal</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                    <span className=\"text-gray-700\">Expand across South Asia</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                    <span className=\"text-gray-700\">Set industry standards</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n\n        {/* Our Story */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 md:p-12\"\n        >\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n            <div>\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">Our Story</h3>\n              <p className=\"text-gray-600 leading-relaxed mb-4\">\n                DigitalBoost was born from a simple observation: many talented Nepalese \n                businesses were struggling to reach their full potential due to limited \n                digital presence and marketing expertise.\n              </p>\n              <p className=\"text-gray-600 leading-relaxed mb-4\">\n                Founded by a team of passionate digital marketers and local entrepreneurs, \n                we started with a mission to bridge this gap and help businesses thrive \n                in the digital age.\n              </p>\n              <p className=\"text-gray-600 leading-relaxed\">\n                Today, we're proud to have helped transform dozens of businesses across \n                Nepal, from traditional family shops to modern tech startups.\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-white p-6 rounded-xl text-center\">\n                <Globe className=\"h-8 w-8 text-blue-600 mx-auto mb-3\" />\n                <div className=\"text-2xl font-bold text-gray-900\">200+</div>\n                <div className=\"text-sm text-gray-600\">Projects Completed</div>\n              </div>\n              <div className=\"bg-white p-6 rounded-xl text-center\">\n                <Zap className=\"h-8 w-8 text-purple-600 mx-auto mb-3\" />\n                <div className=\"text-2xl font-bold text-gray-900\">98%</div>\n                <div className=\"text-sm text-gray-600\">Client Satisfaction</div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAyC;;;;;;sCAG5E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAKlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAKlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAM/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/team-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport Image from \"next/image\";\nimport { Linkedin, Twitter, Mail, User, Palette, BarChart3, Users } from \"lucide-react\";\n\nconst teamMembers = [\n  {\n    name: \"<PERSON><PERSON>\",\n    role: \"Founder & CEO\",\n    icon: User,\n    bio: \"Digital marketing veteran with 8+ years of experience. Passionate about helping Nepalese businesses grow through innovative digital strategies.\",\n    expertise: [\"Strategy\", \"Leadership\", \"Business Development\"],\n    social: {\n      linkedin: \"#\",\n      twitter: \"#\",\n      email: \"<EMAIL>\"\n    }\n  },\n  {\n    name: \"<PERSON><PERSON>\",\n    role: \"Creative Director\",\n    icon: Palette,\n    bio: \"Award-winning designer and content creator. Specializes in visual storytelling and brand development for social media platforms.\",\n    expertise: [\"Design\", \"Content Creation\", \"Branding\"],\n    social: {\n      linkedin: \"#\",\n      twitter: \"#\",\n      email: \"<EMAIL>\"\n    }\n  },\n  {\n    name: \"<PERSON><PERSON>\",\n    role: \"Digital Marketing Manager\",\n    icon: <PERSON><PERSON><PERSON><PERSON>,\n    bio: \"Google Ads certified professional with expertise in paid advertising and analytics. Drives ROI-focused campaigns for our clients.\",\n    expertise: [\"Google Ads\", \"Facebook Ads\", \"Analytics\"],\n    social: {\n      linkedin: \"#\",\n      twitter: \"#\",\n      email: \"<EMAIL>\"\n    }\n  },\n  {\n    name: \"Sita Gurung\",\n    role: \"Social Media Specialist\",\n    icon: Users,\n    bio: \"Social media expert with deep understanding of Nepalese market trends. Creates engaging content that resonates with local audiences.\",\n    expertise: [\"Social Media\", \"Community Management\", \"Influencer Marketing\"],\n    social: {\n      linkedin: \"#\",\n      twitter: \"#\",\n      email: \"<EMAIL>\"\n    }\n  }\n];\n\nexport function TeamSection() {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-green-600 border-green-600\">\n            Meet Our Team\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            The People Behind Your Success\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Our diverse team of digital marketing experts brings together creativity, \n            technical expertise, and deep market knowledge to deliver exceptional results.\n          </p>\n        </motion.div>\n\n        {/* Team Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {teamMembers.map((member, index) => (\n            <motion.div\n              key={member.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full card-hover border-0 shadow-lg group\">\n                <CardContent className=\"p-6 text-center\">\n                  {/* Avatar */}\n                  <motion.div\n                    className=\"w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mb-4 mx-auto\"\n                    whileHover={{ scale: 1.1 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <member.icon className=\"h-10 w-10 text-white\" />\n                  </motion.div>\n                  \n                  {/* Name & Role */}\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{member.name}</h3>\n                  <p className=\"text-blue-600 font-medium mb-4\">{member.role}</p>\n                  \n                  {/* Bio */}\n                  <p className=\"text-gray-600 text-sm leading-relaxed mb-4\">\n                    {member.bio}\n                  </p>\n                  \n                  {/* Expertise Tags */}\n                  <div className=\"flex flex-wrap gap-2 justify-center mb-6\">\n                    {member.expertise.map((skill) => (\n                      <Badge key={skill} variant=\"secondary\" className=\"text-xs\">\n                        {skill}\n                      </Badge>\n                    ))}\n                  </div>\n                  \n                  {/* Social Links */}\n                  <div className=\"flex justify-center space-x-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <a \n                      href={member.social.linkedin} \n                      className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                    >\n                      <Linkedin size={18} />\n                    </a>\n                    <a \n                      href={member.social.twitter} \n                      className=\"text-gray-400 hover:text-blue-400 transition-colors\"\n                    >\n                      <Twitter size={18} />\n                    </a>\n                    <a \n                      href={`mailto:${member.social.email}`} \n                      className=\"text-gray-400 hover:text-green-600 transition-colors\"\n                    >\n                      <Mail size={18} />\n                    </a>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Team Culture */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Our Culture</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"space-y-3\">\n              <div className=\"text-4xl\">🚀</div>\n              <h4 className=\"font-semibold text-gray-900\">Innovation First</h4>\n              <p className=\"text-gray-600 text-sm\">We embrace new technologies and creative approaches to solve complex marketing challenges.</p>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"text-4xl\">🤝</div>\n              <h4 className=\"font-semibold text-gray-900\">Collaboration</h4>\n              <p className=\"text-gray-600 text-sm\">We believe in the power of teamwork and open communication to achieve extraordinary results.</p>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"text-4xl\">📈</div>\n              <h4 className=\"font-semibold text-gray-900\">Continuous Learning</h4>\n              <p className=\"text-gray-600 text-sm\">We invest in our team's growth and stay updated with the latest industry trends and best practices.</p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAQA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,KAAK;QACL,WAAW;YAAC;YAAY;YAAc;SAAuB;QAC7D,QAAQ;YACN,UAAU;YACV,SAAS;YACT,OAAO;QACT;IACF;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,KAAK;QACL,WAAW;YAAC;YAAU;YAAoB;SAAW;QACrD,QAAQ;YACN,UAAU;YACV,SAAS;YACT,OAAO;QACT;IACF;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;QACf,KAAK;QACL,WAAW;YAAC;YAAc;YAAgB;SAAY;QACtD,QAAQ;YACN,UAAU;YACV,SAAS;YACT,OAAO;QACT;IACF;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,KAAK;QACL,WAAW;YAAC;YAAgB;YAAwB;SAAuB;QAC3E,QAAQ;YACN,UAAU;YACV,SAAS;YACT,OAAO;QACT;IACF;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAuC;;;;;;sCAG1E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,8OAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAIzB,8OAAC;4CAAG,WAAU;sDAAwC,OAAO,IAAI;;;;;;sDACjE,8OAAC;4CAAE,WAAU;sDAAkC,OAAO,IAAI;;;;;;sDAG1D,8OAAC;4CAAE,WAAU;sDACV,OAAO,GAAG;;;;;;sDAIb,8OAAC;4CAAI,WAAU;sDACZ,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,sBACrB,8OAAC,iIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAY,WAAU;8DAC9C;mDADS;;;;;;;;;;sDAOhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,OAAO,MAAM,CAAC,QAAQ;oDAC5B,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;;;;;;8DAElB,8OAAC;oDACC,MAAM,OAAO,MAAM,CAAC,OAAO;oDAC3B,WAAU;8DAEV,cAAA,8OAAC,wMAAA,CAAA,UAAO;wDAAC,MAAM;;;;;;;;;;;8DAEjB,8OAAC;oDACC,MAAM,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE;oDACrC,WAAU;8DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BArDf,OAAO,IAAI;;;;;;;;;;8BA+DtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAW;;;;;;sDAC1B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAW;;;;;;sDAC1B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAW;;;;;;sDAC1B,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/achievements-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Award, TrendingUp, Users, Star, Calendar, Target, Rocket, Zap } from \"lucide-react\";\n\nconst achievements = [\n  {\n    icon: Award,\n    title: \"Best Digital Marketing Agency 2023\",\n    description: \"Recognized by Nepal Business Awards for outstanding contribution to digital marketing industry\",\n    year: \"2023\",\n    color: \"from-yellow-500 to-orange-500\"\n  },\n  {\n    icon: TrendingUp,\n    title: \"300% Average ROI\",\n    description: \"Consistently delivered exceptional returns on investment for our clients across all campaigns\",\n    year: \"2024\",\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    icon: Users,\n    title: \"50+ Happy Clients\",\n    description: \"Built lasting partnerships with businesses across Nepal, from startups to established enterprises\",\n    year: \"2024\",\n    color: \"from-blue-500 to-cyan-500\"\n  },\n  {\n    icon: Star,\n    title: \"4.9/5 Client Rating\",\n    description: \"Maintained exceptional client satisfaction scores through dedicated service and results\",\n    year: \"2024\",\n    color: \"from-purple-500 to-pink-500\"\n  }\n];\n\nconst milestones = [\n  {\n    year: \"2019\",\n    title: \"Company Founded\",\n    description: \"Started DigitalBoost with a vision to transform Nepalese businesses\"\n  },\n  {\n    year: \"2020\",\n    title: \"First 10 Clients\",\n    description: \"Successfully onboarded our first batch of clients during challenging times\"\n  },\n  {\n    year: \"2021\",\n    title: \"Team Expansion\",\n    description: \"Grew our team to 8 specialists covering all aspects of digital marketing\"\n  },\n  {\n    year: \"2022\",\n    title: \"100+ Projects\",\n    description: \"Reached the milestone of completing 100 successful marketing campaigns\"\n  },\n  {\n    year: \"2023\",\n    title: \"Industry Recognition\",\n    description: \"Received multiple awards and recognition from industry bodies\"\n  },\n  {\n    year: \"2024\",\n    title: \"Market Leadership\",\n    description: \"Established as one of the leading digital marketing agencies in Nepal\"\n  }\n];\n\nexport function AchievementsSection() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-orange-600 border-orange-600\">\n            Our Achievements\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Milestones & Recognition\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Our journey has been marked by significant achievements and recognition \n            that reflect our commitment to excellence and client success.\n          </p>\n        </motion.div>\n\n        {/* Achievements Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20\">\n          {achievements.map((achievement, index) => (\n            <motion.div\n              key={achievement.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full card-hover border-0 shadow-lg text-center\">\n                <CardContent className=\"p-6\">\n                  <div className={`w-16 h-16 bg-gradient-to-r ${achievement.color} rounded-full flex items-center justify-center mx-auto mb-4`}>\n                    <achievement.icon className=\"h-8 w-8 text-white\" />\n                  </div>\n                  \n                  <Badge variant=\"secondary\" className=\"mb-3\">\n                    {achievement.year}\n                  </Badge>\n                  \n                  <h3 className=\"text-lg font-bold text-gray-900 mb-3\">\n                    {achievement.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 text-sm leading-relaxed\">\n                    {achievement.description}\n                  </p>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Timeline */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <h3 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">Our Journey</h3>\n          \n          <div className=\"relative\">\n            {/* Timeline Line */}\n            <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 to-purple-500 rounded-full\"></div>\n            \n            <div className=\"space-y-12\">\n              {milestones.map((milestone, index) => (\n                <motion.div\n                  key={milestone.year}\n                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.8, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}\n                >\n                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>\n                    <Card className=\"border-0 shadow-lg\">\n                      <CardContent className=\"p-6\">\n                        <div className=\"flex items-center mb-3\">\n                          <Calendar className=\"h-5 w-5 text-blue-600 mr-2\" />\n                          <Badge variant=\"outline\" className=\"text-blue-600 border-blue-600\">\n                            {milestone.year}\n                          </Badge>\n                        </div>\n                        <h4 className=\"text-xl font-bold text-gray-900 mb-2\">\n                          {milestone.title}\n                        </h4>\n                        <p className=\"text-gray-600\">\n                          {milestone.description}\n                        </p>\n                      </CardContent>\n                    </Card>\n                  </div>\n                  \n                  {/* Timeline Dot */}\n                  <div className=\"w-4 h-4 bg-white border-4 border-blue-500 rounded-full z-10\"></div>\n                  \n                  <div className=\"w-1/2\"></div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center\"\n        >\n          <h3 className=\"text-2xl font-bold mb-8\">By the Numbers</h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">200+</div>\n              <div className=\"text-blue-100\">Campaigns Launched</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">5M+</div>\n              <div className=\"text-blue-100\">People Reached</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">98%</div>\n              <div className=\"text-blue-100\">Client Retention</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">24/7</div>\n              <div className=\"text-blue-100\">Support Available</div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,eAAe;IACnB;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,aAAa;IACjB;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAyC;;;;;;sCAG5E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAW,CAAC,2BAA2B,EAAE,YAAY,KAAK,CAAC,2DAA2D,CAAC;sDAC1H,cAAA,8OAAC,YAAY,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAG9B,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,YAAY,IAAI;;;;;;sDAGnB,8OAAC;4CAAG,WAAU;sDACX,YAAY,KAAK;;;;;;sDAGpB,8OAAC;4CAAE,WAAU;sDACV,YAAY,WAAW;;;;;;;;;;;;;;;;;2BArBzB,YAAY,KAAK;;;;;;;;;;8BA8B5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,KAAK;4CAAG;4CACrD,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAW,CAAC,kBAAkB,EAAE,QAAQ,MAAM,IAAI,aAAa,oBAAoB;;8DAEnF,8OAAC;oDAAI,WAAW,CAAC,MAAM,EAAE,QAAQ,MAAM,IAAI,oBAAoB,kBAAkB;8DAC/E,cAAA,8OAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;kEACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,UAAU,IAAI;;;;;;;;;;;;8EAGnB,8OAAC;oEAAG,WAAU;8EACX,UAAU,KAAK;;;;;;8EAElB,8OAAC;oEAAE,WAAU;8EACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;8DAO9B,8OAAC;oDAAI,WAAU;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;;;;;;2CA7BV,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAqC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/cta-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight, Phone, Mail, MessageCircle } from \"lucide-react\";\n\nexport function CTASection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl\"></div>\n        <div className=\"absolute bottom-10 right-10 w-48 h-48 bg-purple-300/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-300/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"space-y-8\"\n        >\n          {/* Main Heading */}\n          <motion.h2 \n            className=\"text-4xl md:text-6xl font-bold leading-tight\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            Start Promoting Your Business{\" \"}\n            <span className=\"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n              Today\n            </span>\n          </motion.h2>\n\n          {/* Subtitle */}\n          <motion.p \n            className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            Ready to transform your business with powerful digital marketing? \n            Get a free strategy session and discover how we can help you grow.\n          </motion.p>\n\n          {/* Benefits List */}\n          <motion.div \n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 my-12\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Free Consultation</h3>\n              <p className=\"text-blue-100\">30-minute strategy session to understand your goals</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Custom Strategy</h3>\n              <p className=\"text-blue-100\">Tailored digital marketing plan for your business</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Quick Results</h3>\n              <p className=\"text-blue-100\">See improvements in your online presence within weeks</p>\n            </div>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Button \n              size=\"lg\" \n              className=\"bg-white text-purple-600 hover:bg-gray-100 font-semibold px-8 py-4 text-lg group\"\n            >\n              Request Free Strategy Session\n              <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n          </motion.div>\n\n          {/* Contact Options */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mt-8\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.0 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Phone className=\"h-5 w-5\" />\n              <span>+977-9800000000</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Mail className=\"h-5 w-5\" />\n              <span><EMAIL></span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <MessageCircle className=\"h-5 w-5\" />\n              <span>WhatsApp Available</span>\n            </div>\n          </motion.div>\n\n          {/* Urgency Element */}\n          <motion.div \n            className=\"mt-8 p-4 bg-yellow-400/20 rounded-lg border border-yellow-400/30\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n            viewport={{ once: true }}\n          >\n            <p className=\"text-yellow-100 font-medium\">\n              🎯 Limited Time: Get 20% off your first campaign when you book this month!\n            </p>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            className=\"flex flex-wrap justify-center items-center gap-8 mt-12 pt-8 border-t border-white/20\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.4 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">50+</div>\n              <div className=\"text-blue-200 text-sm\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">5+</div>\n              <div className=\"text-blue-200 text-sm\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">24/7</div>\n              <div className=\"text-blue-200 text-sm\">Support</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">100%</div>\n              <div className=\"text-blue-200 text-sm\">Satisfaction</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;gCACxB;gCAC+B;8CAC9B,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;;;;;;;sCAMjG,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCACxB;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAKjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;sCAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD", "debugId": null}}]}