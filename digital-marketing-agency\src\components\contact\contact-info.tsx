"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  MessageCircle,
  Facebook,
  Instagram,
  Linkedin,
  Twitter
} from "lucide-react";

const contactMethods = [
  {
    icon: Phone,
    title: "Phone",
    details: ["+977-9800000000", "+977-9811111111"],
    description: "Call us for immediate assistance",
    action: "tel:+9779800000000",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Mail,
    title: "Email",
    details: ["<EMAIL>", "<EMAIL>"],
    description: "Send us an email anytime",
    action: "mailto:<EMAIL>",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: MessageCircle,
    title: "WhatsApp",
    details: ["+977-9800000000"],
    description: "Quick chat on WhatsApp",
    action: "https://wa.me/9779800000000",
    color: "from-green-500 to-green-600"
  }
];

const officeHours = [
  { day: "Sunday - Friday", hours: "9:00 AM - 6:00 PM" },
  { day: "Saturday", hours: "10:00 AM - 4:00 PM" },
  { day: "Public Holidays", hours: "Closed" }
];

const socialLinks = [
  { icon: Facebook, name: "Facebook", url: "#", color: "text-blue-600" },
  { icon: Instagram, name: "Instagram", url: "#", color: "text-pink-600" },
  { icon: Linkedin, name: "LinkedIn", url: "#", color: "text-blue-700" },
  { icon: Twitter, name: "Twitter", url: "#", color: "text-blue-400" }
];

const faqs = [
  {
    question: "How quickly can you start working on my project?",
    answer: "We can typically start within 1-2 weeks after our initial consultation and strategy approval."
  },
  {
    question: "Do you work with businesses outside Biratnagar?",
    answer: "Yes! We work with businesses across Nepal and can manage everything remotely."
  },
  {
    question: "What's included in your free consultation?",
    answer: "A comprehensive review of your current digital presence, competitor analysis, and a customized strategy recommendation."
  }
];

export function ContactInfo() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, x: 30 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          {/* Contact Methods */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Get In Touch</h2>
            <div className="space-y-4">
              {contactMethods.map((method, index) => (
                <Card key={method.title} className="border-0 shadow-lg card-hover">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${method.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                        <method.icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900 mb-1">{method.title}</h3>
                        <div className="space-y-1 mb-2">
                          {method.details.map((detail) => (
                            <p key={detail} className="text-gray-700">{detail}</p>
                          ))}
                        </div>
                        <p className="text-gray-600 text-sm">{method.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Office Hours */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Office Hours</h3>
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-gray-900 mb-3">When We're Available</h4>
                    <div className="space-y-2">
                      {officeHours.map((schedule) => (
                        <div key={schedule.day} className="flex justify-between">
                          <span className="text-gray-700">{schedule.day}</span>
                          <span className="text-gray-600">{schedule.hours}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Emergency support available 24/7
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Office Location */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Our Location</h3>
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-gray-900 mb-2">DigitalBoost Office</h4>
                    <p className="text-gray-700 mb-2">
                      Main Road, Biratnagar-15<br />
                      Morang, Nepal
                    </p>
                    <p className="text-gray-600 text-sm">
                      Located in the heart of Biratnagar's business district, 
                      easily accessible by public transport.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Social Media */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Follow Us</h3>
            <div className="grid grid-cols-2 gap-4">
              {socialLinks.map((social) => (
                <Card key={social.name} className="border-0 shadow-lg card-hover cursor-pointer">
                  <CardContent className="p-4 text-center">
                    <social.icon className={`h-8 w-8 ${social.color} mx-auto mb-2`} />
                    <p className="font-medium text-gray-900">{social.name}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Quick FAQs */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Quick Answers</h3>
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <Card key={index} className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <h4 className="font-semibold text-gray-900 mb-2">{faq.question}</h4>
                    <p className="text-gray-600 text-sm">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
