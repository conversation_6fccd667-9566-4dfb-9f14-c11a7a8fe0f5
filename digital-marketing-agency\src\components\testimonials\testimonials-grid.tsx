"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, User, Briefcase, Laptop, Store, Coffee, GraduationCap } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Owner",
    company: "Sharma Electronics",
    icon: Store,
    rating: 5,
    text: "DigitalBoost transformed our local electronics store into a regional brand. Our Facebook page went from 200 to 15,000 followers in just 6 months, and sales increased by 300%! Their team understands the Nepalese market perfectly.",
    result: "300% Sales Increase",
    industry: "Retail",
    duration: "6 months"
  },
  {
    name: "<PERSON><PERSON> Thapa",
    role: "Marketing Manager", 
    company: "Himalayan Cafe Chain",
    icon: Coffee,
    rating: 5,
    text: "Their content creation is exceptional! The food photography and Instagram reels they created for our cafe chain helped us become the most followed restaurant in Biratnagar. The engagement rates are incredible.",
    result: "50K+ Instagram Followers",
    industry: "Food & Beverage",
    duration: "8 months"
  },
  {
    name: "<PERSON><PERSON>",
    role: "Founder",
    company: "TechStart Nepal",
    icon: Laptop,
    rating: 5,
    text: "Professional, creative, and results-driven. DigitalBoost's Google Ads campaigns helped us acquire 500+ new customers in our first quarter. Their ROI-focused approach is exactly what startups need.",
    result: "500+ New Customers",
    industry: "Technology",
    duration: "4 months"
  },
  {
    name: "Sita Gurung",
    role: "Principal",
    company: "Everest Academy",
    icon: GraduationCap,
    rating: 5,
    text: "We were struggling with declining enrollment until DigitalBoost created targeted Facebook campaigns for parents. Student inquiries increased by 400% and we're now at full capacity!",
    result: "400% Inquiry Increase",
    industry: "Education",
    duration: "5 months"
  },
  {
    name: "Krishna Shrestha",
    role: "CEO",
    company: "Nepal Real Estate",
    icon: User,
    rating: 5,
    text: "The virtual property tours and lead generation campaigns they created revolutionized our sales process. We've sold more properties in 6 months than we did in the previous 2 years combined.",
    result: "250% Property Sales",
    industry: "Real Estate",
    duration: "7 months"
  },
  {
    name: "Maya Tamang",
    role: "Founder",
    company: "Kathmandu Fashion Hub",
    icon: Briefcase,
    rating: 5,
    text: "Their influencer marketing strategy and social commerce setup took our online fashion store to the next level. The ROAS of 5.2x speaks for itself. Highly recommended for e-commerce businesses!",
    result: "5.2x ROAS",
    industry: "Fashion",
    duration: "6 months"
  }
];

export function TestimonialsGrid() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-green-600 border-green-600">
            Client Success Stories
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Real Results from Real Businesses
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our clients across different 
            industries have achieved with our digital marketing strategies.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full card-hover border-0 shadow-lg relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-bl-full opacity-10"></div>
                
                <CardContent className="p-6 relative">
                  <Quote className="h-8 w-8 text-blue-500 mb-4" />
                  
                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  {/* Testimonial Text */}
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    "{testimonial.text}"
                  </p>

                  {/* Result Badge */}
                  <Badge className="mb-4 bg-green-100 text-green-800 hover:bg-green-100">
                    {testimonial.result}
                  </Badge>

                  {/* Industry & Duration */}
                  <div className="flex gap-2 mb-6">
                    <Badge variant="outline" className="text-blue-600 border-blue-600">
                      {testimonial.industry}
                    </Badge>
                    <Badge variant="outline" className="text-purple-600 border-purple-600">
                      {testimonial.duration}
                    </Badge>
                  </div>

                  {/* Client Info */}
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <testimonial.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                      <p className="text-sm text-gray-600">{testimonial.role}</p>
                      <p className="text-sm font-medium text-blue-600">{testimonial.company}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div 
          className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold mb-8">Client Satisfaction Metrics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">98%</div>
              <div className="text-blue-100">Client Retention Rate</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">4.9/5</div>
              <div className="text-blue-100">Average Rating</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">250%</div>
              <div className="text-blue-100">Average ROI</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">24hrs</div>
              <div className="text-blue-100">Response Time</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
