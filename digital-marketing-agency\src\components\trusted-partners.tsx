"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";

import { Facebook, Chrome, Music, Palette, Mail, ShoppingCart, FileText, Heart } from "lucide-react";

const partners = [
  { name: "Meta", icon: Facebook, description: "Official Meta Business Partner", color: "text-blue-600" },
  { name: "Google", icon: Chrome, description: "Google Ads Certified Partner", color: "text-red-600" },
  { name: "TikTok", icon: Music, description: "TikTok for Business Partner", color: "text-black" },
  { name: "<PERSON>va", icon: Palette, description: "Canva Pro Design Partner", color: "text-purple-600" },
  { name: "Mailchimp", icon: Mail, description: "Email Marketing Partner", color: "text-yellow-600" },
  { name: "Shopify", icon: ShoppingCart, description: "E-commerce Solutions", color: "text-green-600" },
  { name: "WordPress", icon: FileText, description: "Website Development", color: "text-blue-800" },
  { name: "HubS<PERSON>", icon: Heart, description: "CRM & Marketing Automation", color: "text-orange-600" },
];

const localClients = [
  "Biratnagar Chamber of Commerce",
  "Nepal Tourism Board",
  "Local Restaurants & Cafes",
  "Fashion Boutiques",
  "Educational Institutions",
  "Healthcare Centers",
  "Real Estate Companies",
  "Tech Startups"
];

export function TrustedPartners() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-purple-600 border-purple-600">
            Trusted Partners
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Partnered with Industry Leaders
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We collaborate with top platforms and tools to deliver exceptional results for our clients.
          </p>
        </motion.div>

        {/* Platform Partners */}
        <motion.div 
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">Platform Partners</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {partners.map((partner, index) => (
              <motion.div
                key={partner.name}
                className="bg-gray-50 rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
              >
                <partner.icon className={`h-12 w-12 mb-3 mx-auto ${partner.color}`} />
                <h4 className="font-bold text-gray-900 mb-2">{partner.name}</h4>
                <p className="text-sm text-gray-600">{partner.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Client Industries */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">Industries We Serve</h3>
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localClients.map((client, index) => (
                <motion.div
                  key={client}
                  className="bg-white rounded-lg p-4 text-center shadow-sm"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.05 }}
                  viewport={{ once: true }}
                >
                  <p className="text-gray-700 font-medium">{client}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Scrolling Logos Animation */}
        <motion.div 
          className="mt-16 overflow-hidden"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex space-x-8 animate-marquee">
            {[...partners, ...partners].map((partner, index) => (
              <div key={index} className="flex items-center space-x-2 whitespace-nowrap">
                <partner.icon className={`h-6 w-6 ${partner.color}`} />
                <span className="font-semibold text-gray-700">{partner.name}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div 
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="space-y-2">
            <div className="text-3xl font-bold text-blue-600">100%</div>
            <div className="text-gray-600">Client Satisfaction</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-purple-600">5+</div>
            <div className="text-gray-600">Years of Partnership</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-green-600">24/7</div>
            <div className="text-gray-600">Platform Support</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
