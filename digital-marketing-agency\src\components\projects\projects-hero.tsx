"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, TrendingUp, Users, Target } from "lucide-react";

const achievements = [
  {
    icon: TrendingUp,
    value: "300%",
    label: "Average ROI Increase",
    color: "text-green-600"
  },
  {
    icon: Users,
    value: "2M+",
    label: "People Reached",
    color: "text-blue-600"
  },
  {
    icon: Target,
    value: "95%",
    label: "Campaign Success Rate",
    color: "text-purple-600"
  }
];

export function ProjectsHero() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Badge variant="outline" className="mb-4 text-blue-600 border-blue-600">
              Our Work
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight mb-6">
              Success Stories &{" "}
              <span className="gradient-text">Case Studies</span>
            </h1>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
              Discover how we've helped businesses across Nepal achieve remarkable growth 
              through strategic digital marketing campaigns. Each project tells a story 
              of transformation and success.
            </p>

            {/* Achievement Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white p-6 rounded-xl shadow-lg"
                >
                  <achievement.icon className={`h-8 w-8 ${achievement.color} mx-auto mb-3`} />
                  <div className={`text-3xl font-bold ${achievement.color} mb-2`}>
                    {achievement.value}
                  </div>
                  <div className="text-gray-600">{achievement.label}</div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button size="lg" className="gradient-bg text-white px-8 py-4 text-lg group">
                View All Projects
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              
              <Button variant="outline" size="lg" className="px-8 py-4 text-lg">
                Start Your Success Story
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
