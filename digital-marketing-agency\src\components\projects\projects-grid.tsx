"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import {
  ExternalLink,
  TrendingUp,
  Users,
  Eye,
  ShoppingCart,
  Store,
  Coffee,
  Laptop,
  Heart,
  GraduationCap,
  Home
} from "lucide-react";

const projects = [
  {
    id: 1,
    title: "Sharma Electronics - Regional Expansion",
    client: "Sharma Electronics",
    industry: "Electronics",
    icon: Store,
    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=250&fit=crop&crop=center",
    description: "Transformed a local electronics store into a regional brand through strategic social media marketing and targeted advertising.",
    challenge: "Limited brand awareness beyond local area, low online presence, traditional marketing approach",
    solution: "Comprehensive social media strategy, influencer partnerships, targeted Facebook and Google ads",
    results: [
      { metric: "Sales Increase", value: "300%", icon: TrendingUp },
      { metric: "Social Followers", value: "15K+", icon: Users },
      { metric: "Website Traffic", value: "500%", icon: Eye }
    ],
    duration: "6 months",
    platforms: ["Facebook", "Instagram", "Google Ads"],
    category: "retail",
    featured: true
  },
  {
    id: 2,
    title: "Himalayan Cafe Chain - Brand Building",
    client: "Himalayan Cafe Chain",
    industry: "Food & Beverage",
    icon: Coffee,
    description: "Built a strong social media presence for a cafe chain, focusing on food photography and customer engagement.",
    challenge: "New brand with no social media presence, competitive food market, need for brand differentiation",
    solution: "Professional food photography, Instagram-first strategy, user-generated content campaigns",
    results: [
      { metric: "Instagram Followers", value: "50K+", icon: Users },
      { metric: "Engagement Rate", value: "8.5%", icon: Heart },
      { metric: "Store Visits", value: "200%", icon: Eye }
    ],
    duration: "8 months",
    platforms: ["Instagram", "Facebook", "TikTok"],
    category: "food",
    featured: true
  },
  {
    id: 3,
    title: "TechStart Nepal - Lead Generation",
    client: "TechStart Nepal",
    industry: "Technology",
    icon: Laptop,
    description: "Generated qualified leads for a tech startup through LinkedIn marketing and Google Ads campaigns.",
    challenge: "B2B lead generation, limited budget, competitive tech market",
    solution: "LinkedIn advertising, content marketing, Google Ads optimization, landing page creation",
    results: [
      { metric: "Qualified Leads", value: "500+", icon: Users },
      { metric: "Cost per Lead", value: "-60%", icon: TrendingUp },
      { metric: "Conversion Rate", value: "12%", icon: ShoppingCart }
    ],
    duration: "4 months",
    platforms: ["LinkedIn", "Google Ads", "Website"],
    category: "technology",
    featured: false
  },
  {
    id: 4,
    title: "Everest Academy - Student Enrollment",
    client: "Everest Academy",
    industry: "Education",
    icon: GraduationCap,
    description: "Increased student enrollment for a private academy through targeted social media campaigns and parent engagement.",
    challenge: "Declining enrollment, competition from other schools, limited marketing budget",
    solution: "Parent-focused Facebook campaigns, success story content, virtual school tours",
    results: [
      { metric: "Enrollment Increase", value: "150%", icon: TrendingUp },
      { metric: "Inquiry Calls", value: "400%", icon: Users },
      { metric: "Social Reach", value: "100K+", icon: Eye }
    ],
    duration: "5 months",
    platforms: ["Facebook", "Instagram", "YouTube"],
    category: "education",
    featured: false
  },
  {
    id: 5,
    title: "Nepal Real Estate - Property Sales",
    client: "Nepal Real Estate",
    industry: "Real Estate",
    icon: Home,
    description: "Boosted property sales through virtual tours, targeted advertising, and lead nurturing campaigns.",
    challenge: "Slow property sales, need for virtual showcasing, lead qualification",
    solution: "Virtual property tours, Facebook lead ads, email marketing automation",
    results: [
      { metric: "Property Sales", value: "250%", icon: TrendingUp },
      { metric: "Qualified Leads", value: "800+", icon: Users },
      { metric: "Tour Requests", value: "300%", icon: Eye }
    ],
    duration: "7 months",
    platforms: ["Facebook", "Instagram", "YouTube", "Email"],
    category: "realestate",
    featured: false
  },
  {
    id: 6,
    title: "Kathmandu Fashion Hub - E-commerce Growth",
    client: "Kathmandu Fashion Hub",
    industry: "Fashion",
    icon: Heart,
    description: "Grew online fashion store through influencer marketing, social commerce, and targeted advertising.",
    challenge: "Low online sales, brand awareness, competition from established brands",
    solution: "Influencer collaborations, Instagram shopping, Facebook dynamic ads",
    results: [
      { metric: "Online Sales", value: "400%", icon: ShoppingCart },
      { metric: "Social Followers", value: "25K+", icon: Users },
      { metric: "ROAS", value: "5.2x", icon: TrendingUp }
    ],
    duration: "6 months",
    platforms: ["Instagram", "Facebook", "TikTok"],
    category: "fashion",
    featured: true
  }
];

const categories = [
  { id: "all", name: "All Projects" },
  { id: "retail", name: "Retail" },
  { id: "food", name: "Food & Beverage" },
  { id: "technology", name: "Technology" },
  { id: "education", name: "Education" },
  { id: "realestate", name: "Real Estate" },
  { id: "fashion", name: "Fashion" }
];

export function ProjectsGrid() {
  const [activeCategory, setActiveCategory] = useState("all");
  const [showAll, setShowAll] = useState(false);

  const filteredProjects = projects.filter(project => 
    activeCategory === "all" || project.category === activeCategory
  );

  const displayedProjects = showAll ? filteredProjects : filteredProjects.slice(0, 6);

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Filter Tabs */}
        <motion.div 
          className="flex flex-wrap justify-center gap-2 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={activeCategory === category.id ? "default" : "outline"}
              onClick={() => setActiveCategory(category.id)}
              className={activeCategory === category.id ? "gradient-bg text-white" : ""}
            >
              {category.name}
            </Button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayedProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full card-hover border-0 shadow-lg relative overflow-hidden group">
                {project.featured && (
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                      Featured
                    </Badge>
                  </div>
                )}
                
                <CardContent className="p-6">
                  {/* Project Header */}
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <project.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                        {project.title}
                      </h3>
                      <p className="text-sm text-gray-600">{project.client} • {project.industry}</p>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {project.description}
                  </p>

                  {/* Results */}
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    {project.results.map((result) => (
                      <div key={result.metric} className="text-center">
                        <result.icon className="h-4 w-4 text-blue-600 mx-auto mb-1" />
                        <div className="text-lg font-bold text-gray-900">{result.value}</div>
                        <div className="text-xs text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>

                  {/* Platforms */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {project.platforms.map((platform) => (
                      <Badge key={platform} variant="secondary" className="text-xs">
                        {platform}
                      </Badge>
                    ))}
                  </div>

                  {/* Duration & CTA */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">{project.duration}</span>
                    <Button size="sm" variant="ghost" className="text-blue-600 hover:text-blue-700">
                      View Details
                      <ExternalLink className="ml-1 h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Load More Button */}
        {!showAll && filteredProjects.length > 6 && (
          <motion.div 
            className="text-center mt-12"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Button 
              onClick={() => setShowAll(true)}
              size="lg" 
              variant="outline"
              className="px-8"
            >
              Load More Projects
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  );
}
