"use client";

import { motion } from "framer-motion";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Star, Quote } from "lucide-react";

const videoTestimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    company: "Sharma Electronics",
    thumbnail: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop&crop=face",
    duration: "2:15",
    preview: "See how we helped transform a local electronics store into a regional brand with 300% sales growth...",
    featured: true
  },
  {
    id: 2,
    name: "<PERSON><PERSON> Tha<PERSON>", 
    company: "Himalayan Cafe Chain",
    thumbnail: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=200&fit=crop&crop=face",
    duration: "1:45",
    preview: "Discover the content strategy that helped us reach 50K+ Instagram followers and become Biratnagar's most followed restaurant...",
    featured: false
  },
  {
    id: 3,
    name: "<PERSON><PERSON> <PERSON>",
    company: "TechStart Nepal", 
    thumbnail: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=200&fit=crop&crop=face",
    duration: "3:20",
    preview: "Learn how strategic Google Ads campaigns helped us acquire 500+ customers in just 4 months...",
    featured: false
  }
];

const writtenTestimonials = [
  {
    text: "The team at DigitalBoost doesn't just deliver results – they become true partners in your business growth. Their strategic approach and deep understanding of the Nepalese market is unmatched.",
    author: "Krishna Shrestha",
    company: "Nepal Real Estate",
    rating: 5
  },
  {
    text: "What impressed me most was their ability to translate complex digital marketing concepts into simple, actionable strategies that our team could understand and implement.",
    author: "Maya Tamang", 
    company: "Kathmandu Fashion Hub",
    rating: 5
  }
];

export function TestimonialsVideo() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-purple-600 border-purple-600">
            Video Testimonials
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Hear Directly from Our Clients
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Watch our clients share their success stories and the impact 
            our digital marketing strategies have had on their businesses.
          </p>
        </motion.div>

        {/* Video Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {videoTestimonials.map((video, index) => (
            <motion.div
              key={video.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className={`h-full card-hover border-0 shadow-lg relative overflow-hidden ${video.featured ? 'ring-2 ring-blue-500' : ''}`}>
                {video.featured && (
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                      Featured
                    </Badge>
                  </div>
                )}
                
                <CardContent className="p-0">
                  {/* Video Thumbnail */}
                  <div className="relative h-48 bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center group cursor-pointer">
                    {/* Placeholder for video thumbnail */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20"></div>
                    
                    <div className="relative z-10 text-center">
                      <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                        <Play className="h-8 w-8 text-blue-600 ml-1" />
                      </div>
                      <Badge variant="secondary" className="bg-black/50 text-white">
                        {video.duration}
                      </Badge>
                    </div>
                    
                    {/* Decorative elements */}
                    <div className="absolute top-4 left-4 w-8 h-8 bg-white/20 rounded-full"></div>
                    <div className="absolute bottom-4 right-4 w-6 h-6 bg-white/30 rounded-full"></div>
                  </div>
                  
                  {/* Video Info */}
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    
                    <h3 className="font-bold text-gray-900 mb-1">{video.name}</h3>
                    <p className="text-sm text-blue-600 mb-3">{video.company}</p>
                    
                    <p className="text-gray-600 text-sm leading-relaxed mb-4">
                      {video.preview}
                    </p>
                    
                    <Button variant="outline" size="sm" className="w-full">
                      <Play className="mr-2 h-4 w-4" />
                      Watch Testimonial
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Written Testimonials */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-8">
            More Client Feedback
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {writtenTestimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-blue-500 mb-4" />
                  
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <p className="text-gray-700 leading-relaxed mb-6 italic">
                    "{testimonial.text}"
                  </p>
                  
                  <div className="border-t pt-4">
                    <h4 className="font-bold text-gray-900">{testimonial.author}</h4>
                    <p className="text-blue-600 text-sm">{testimonial.company}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Want to Share Your Success Story?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join our growing list of successful clients. Let's create your 
              testimonial video showcasing the amazing results we'll achieve together.
            </p>
            <Button size="lg" className="gradient-bg text-white">
              Start Your Success Story
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
