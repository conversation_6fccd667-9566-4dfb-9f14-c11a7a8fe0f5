# DigitalBoost - Digital Marketing Agency Website

A modern, fully responsive website for a digital marketing and advertising agency based in Biratnagar, Nepal. Built with Next.js 15, Tailwind CSS, and ShadCN UI components.

## 🌟 Features

### 🏠 **Complete Website Structure**
- **Home Page**: Hero section, services overview, trusted partners, testimonials, and CTA
- **About Page**: Mission/vision, team profiles, achievements, and company story
- **Services Page**: Detailed service offerings with accordion UI and pricing
- **Projects Page**: Case studies with filtering and success metrics
- **Testimonials Page**: Client reviews with video testimonials section
- **Contact Page**: Contact form, office information, and interactive elements

### 💻 **Tech Stack**
- **Next.js 15** with App Router
- **Tailwind CSS v4** for styling
- **ShadCN UI** components (accordion, card, button, badge, dialog, input, etc.)
- **Framer Motion** for smooth animations
- **TypeScript** for type safety
- **Lucide React** for consistent iconography

### 🎨 **Design Features**
- Fully responsive design (mobile-first approach)
- Modern gradient color scheme (blue to purple)
- Smooth animations and hover effects
- Professional typography with Inter font
- Consistent component design system
- Accessible UI components

### 🔍 **SEO Optimized**
- **Targeted Keywords**:
  - "digital marketing agency Biratnagar"
  - "Facebook ads Nepal"
  - "Instagram marketing Biratnagar"
  - "best social media agency Nepal"
- **generateMetadata()** for dynamic SEO tags
- **robots.txt** for search engine crawling
- **sitemap.xml** for better indexing
- **Structured data** (JSON-LD) for rich snippets
- Optimized meta descriptions and titles

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd digital-marketing-agency
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3002](http://localhost:3002)

### Build for Production

```bash
npm run build
npm start
```

## 📱 Key Features Implemented

- ✅ **Responsive Design**: Mobile-first approach with Tailwind CSS
- ✅ **Modern UI**: ShadCN components with consistent design system
- ✅ **Smooth Animations**: Framer Motion for enhanced user experience
- ✅ **SEO Optimized**: Complete meta tags, sitemap, and structured data
- ✅ **Performance**: Next.js 15 with optimized loading and rendering
- ✅ **Accessibility**: WCAG compliant components and navigation
- ✅ **Professional Content**: Industry-specific copy and testimonials
- ✅ **Contact Forms**: Functional contact forms with validation
- ✅ **Case Studies**: Detailed project showcases with results

## 📞 Contact Information

**DigitalBoost - Digital Marketing Agency**
- **Location**: Biratnagar, Nepal
- **Phone**: +977-9800000000
- **Email**: <EMAIL>
- **Services**: Social Media Marketing, Content Creation, Paid Advertising, Brand Development

---

**Built with ❤️ for Nepalese businesses looking to grow their digital presence.**
