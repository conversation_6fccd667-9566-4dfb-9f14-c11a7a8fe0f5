"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  MessageSquare, 
  Search, 
  PenTool, 
  Rocket, 
  BarChart3, 
  RefreshCw 
} from "lucide-react";

const processSteps = [
  {
    step: "01",
    icon: MessageSquare,
    title: "Discovery & Consultation",
    description: "We start with a comprehensive consultation to understand your business goals, target audience, and current digital presence.",
    details: [
      "Business goals assessment",
      "Target audience analysis",
      "Competitor research",
      "Current digital audit"
    ],
    color: "from-blue-500 to-cyan-500"
  },
  {
    step: "02",
    icon: Search,
    title: "Strategy Development",
    description: "Based on our findings, we develop a customized digital marketing strategy tailored to your specific needs and objectives.",
    details: [
      "Custom strategy creation",
      "Platform selection",
      "Content planning",
      "Timeline development"
    ],
    color: "from-purple-500 to-pink-500"
  },
  {
    step: "03",
    icon: PenTool,
    title: "Content Creation",
    description: "Our creative team produces high-quality content including graphics, videos, copy, and other materials needed for your campaigns.",
    details: [
      "Visual content design",
      "Video production",
      "Copywriting",
      "Brand asset creation"
    ],
    color: "from-green-500 to-emerald-500"
  },
  {
    step: "04",
    icon: Rocket,
    title: "Campaign Launch",
    description: "We execute your digital marketing campaigns across selected platforms with careful monitoring and real-time adjustments.",
    details: [
      "Campaign setup",
      "Content publishing",
      "Ad campaign launch",
      "Initial monitoring"
    ],
    color: "from-orange-500 to-red-500"
  },
  {
    step: "05",
    icon: BarChart3,
    title: "Monitor & Analyze",
    description: "Continuous monitoring of campaign performance with detailed analytics and insights to track progress toward your goals.",
    details: [
      "Performance tracking",
      "Analytics reporting",
      "ROI measurement",
      "Insight generation"
    ],
    color: "from-indigo-500 to-purple-500"
  },
  {
    step: "06",
    icon: RefreshCw,
    title: "Optimize & Scale",
    description: "Based on performance data, we continuously optimize campaigns and scale successful strategies for maximum impact.",
    details: [
      "Campaign optimization",
      "Strategy refinement",
      "Scaling successful tactics",
      "Continuous improvement"
    ],
    color: "from-pink-500 to-rose-500"
  }
];

export function ServicesProcess() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-green-600 border-green-600">
            Our Process
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            How We Work With You
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our proven 6-step process ensures that every project is executed with precision, 
            creativity, and a focus on delivering measurable results.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {processSteps.map((step, index) => (
            <motion.div
              key={step.step}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full card-hover border-0 shadow-lg relative overflow-hidden">
                <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${step.color} rounded-bl-full opacity-10`}></div>
                
                <CardContent className="p-6 relative">
                  {/* Step Number */}
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-lg flex items-center justify-center`}>
                      <step.icon className="h-6 w-6 text-white" />
                    </div>
                    <Badge variant="outline" className="text-gray-500">
                      {step.step}
                    </Badge>
                  </div>

                  {/* Title & Description */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {step.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Details */}
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Key Activities:</h4>
                    <ul className="space-y-1">
                      {step.details.map((detail) => (
                        <li key={detail} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          <span className="text-sm text-gray-600">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Process Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Typical Project Timeline</h3>
          
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">Week 1</div>
                <div className="text-gray-600">Discovery & Strategy</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">Week 2</div>
                <div className="text-gray-600">Content Creation</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">Week 3</div>
                <div className="text-gray-600">Campaign Launch</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-2">Ongoing</div>
                <div className="text-gray-600">Monitor & Optimize</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
