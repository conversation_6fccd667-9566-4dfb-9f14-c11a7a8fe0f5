"use client";

import { motion } from "framer-motion";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { 
  Share2, 
  Users, 
  Camera, 
  Target,
  BarChart3,
  Megaphone,
  Globe,
  Mail,
  CheckCircle
} from "lucide-react";

const services = [
  {
    id: "social-media",
    icon: Share2,
    title: "Social Media Strategy & Management",
    description: "Comprehensive social media marketing across all major platforms",
    color: "from-blue-500 to-cyan-500",
    features: [
      "Platform-specific content strategy",
      "Daily content creation and posting",
      "Community management and engagement",
      "Social media advertising campaigns",
      "Performance analytics and reporting",
      "Competitor analysis and insights"
    ],
    platforms: ["Facebook", "Instagram", "TikTok", "LinkedIn", "Twitter"],
    pricing: "Starting from NPR 25,000/month"
  },
  {
    id: "influencer",
    icon: Users,
    title: "Influencer Marketing & Collaborations",
    description: "Connect with top influencers to amplify your brand message",
    color: "from-purple-500 to-pink-500",
    features: [
      "Influencer identification and outreach",
      "Campaign strategy and planning",
      "Contract negotiation and management",
      "Content collaboration and approval",
      "Performance tracking and ROI analysis",
      "Long-term partnership development"
    ],
    platforms: ["Instagram", "TikTok", "YouTube", "Facebook"],
    pricing: "Starting from NPR 15,000/campaign"
  },
  {
    id: "content",
    icon: Camera,
    title: "Content Creation & Production",
    description: "Professional content that tells your brand story effectively",
    color: "from-green-500 to-emerald-500",
    features: [
      "Professional photography and videography",
      "Graphic design and visual branding",
      "Copywriting and content planning",
      "Video editing and post-production",
      "Brand guidelines development",
      "Content calendar management"
    ],
    platforms: ["All Platforms", "Website", "Print Media"],
    pricing: "Starting from NPR 20,000/month"
  },
  {
    id: "advertising",
    icon: Target,
    title: "Paid Advertising & PPC",
    description: "Data-driven advertising campaigns for maximum ROI",
    color: "from-orange-500 to-red-500",
    features: [
      "Meta Ads (Facebook & Instagram)",
      "Google Ads and Google Shopping",
      "TikTok advertising campaigns",
      "LinkedIn advertising for B2B",
      "Campaign optimization and A/B testing",
      "Conversion tracking and analytics"
    ],
    platforms: ["Meta", "Google", "TikTok", "LinkedIn"],
    pricing: "Starting from NPR 30,000/month + ad spend"
  },
  {
    id: "analytics",
    icon: BarChart3,
    title: "Analytics & Performance Tracking",
    description: "Comprehensive reporting and data-driven insights",
    color: "from-indigo-500 to-purple-500",
    features: [
      "Google Analytics setup and monitoring",
      "Social media analytics and reporting",
      "ROI tracking and measurement",
      "Custom dashboard creation",
      "Monthly performance reports",
      "Strategic recommendations"
    ],
    platforms: ["Google Analytics", "Social Platforms", "Custom Tools"],
    pricing: "Starting from NPR 10,000/month"
  },
  {
    id: "branding",
    icon: Megaphone,
    title: "Brand Development & Strategy",
    description: "Build a strong brand identity that resonates with your audience",
    color: "from-pink-500 to-rose-500",
    features: [
      "Brand identity design and development",
      "Logo design and brand guidelines",
      "Brand positioning and messaging",
      "Market research and analysis",
      "Brand voice and tone development",
      "Brand consistency across all channels"
    ],
    platforms: ["All Channels", "Digital & Print"],
    pricing: "Starting from NPR 50,000/project"
  }
];

export function ServicesAccordion() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-purple-600 border-purple-600">
            Detailed Services
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Everything You Need to Succeed Online
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our comprehensive range of digital marketing services designed 
            to help your business thrive in the digital landscape.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Accordion type="single" collapsible className="space-y-4">
            {services.map((service, index) => (
              <AccordionItem 
                key={service.id} 
                value={service.id}
                className="border border-gray-200 rounded-lg px-6 shadow-sm hover:shadow-md transition-shadow"
              >
                <AccordionTrigger className="hover:no-underline py-6">
                  <div className="flex items-center space-x-4 text-left">
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${service.color}`}>
                      <service.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">{service.title}</h3>
                      <p className="text-gray-600 mt-1">{service.description}</p>
                    </div>
                  </div>
                </AccordionTrigger>
                
                <AccordionContent className="pb-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4">
                    {/* Features */}
                    <div className="lg:col-span-2">
                      <h4 className="font-semibold text-gray-900 mb-4">What's Included:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {service.features.map((feature) => (
                          <div key={feature} className="flex items-start space-x-2">
                            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Platforms & Pricing */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Platforms:</h4>
                        <div className="flex flex-wrap gap-2">
                          {service.platforms.map((platform) => (
                            <Badge key={platform} variant="secondary">
                              {platform}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Pricing:</h4>
                        <p className="text-blue-600 font-medium">{service.pricing}</p>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </motion.div>
      </div>
    </section>
  );
}
