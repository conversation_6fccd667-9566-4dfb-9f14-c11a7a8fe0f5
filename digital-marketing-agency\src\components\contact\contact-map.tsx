"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Navigation, Car, Bus } from "lucide-react";

const directions = [
  {
    icon: Car,
    method: "By Car",
    description: "Free parking available in front of our office building",
    time: "5 min from city center"
  },
  {
    icon: Bus,
    method: "By Public Transport",
    description: "Bus stop directly in front of the building",
    time: "Regular buses every 10 minutes"
  },
  {
    icon: Navigation,
    method: "GPS Coordinates",
    description: "26.4525° N, 87.2718° E",
    time: "Use any navigation app"
  }
];

export function ContactMap() {
  return (
    <section id="map" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <Badge variant="outline" className="mb-4 text-purple-600 border-purple-600">
            Find Us
          </Badge>
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Visit Our Office
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Located in the heart of Biratnagar, our office is easily accessible 
            and we'd love to meet you in person to discuss your digital marketing needs.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Map Placeholder */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-2"
          >
            <Card className="border-0 shadow-xl overflow-hidden">
              <CardContent className="p-0">
                {/* Map Placeholder - In a real implementation, you'd use Google Maps or similar */}
                <div className="relative h-96 bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">DigitalBoost Office</h3>
                    <p className="text-gray-600 mb-4">Main Road, Biratnagar-15, Morang, Nepal</p>
                    <Badge className="bg-blue-600 text-white">
                      Interactive Map Coming Soon
                    </Badge>
                  </div>
                  
                  {/* Decorative elements */}
                  <div className="absolute top-4 left-4 w-8 h-8 bg-blue-200 rounded-full opacity-50"></div>
                  <div className="absolute bottom-4 right-4 w-12 h-12 bg-purple-200 rounded-full opacity-30"></div>
                  <div className="absolute top-1/3 right-8 w-6 h-6 bg-green-200 rounded-full opacity-40"></div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Directions */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-6">How to Reach Us</h3>
            
            {directions.map((direction, index) => (
              <Card key={direction.method} className="border-0 shadow-lg card-hover">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <direction.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-gray-900 mb-1">{direction.method}</h4>
                      <p className="text-gray-600 text-sm mb-2">{direction.description}</p>
                      <Badge variant="outline" className="text-blue-600 border-blue-600">
                        {direction.time}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Additional Info */}
            <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
              <CardContent className="p-6">
                <h4 className="font-bold text-gray-900 mb-3">Office Features</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Free parking available</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Wheelchair accessible</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Conference room available</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Free WiFi for visitors</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Prefer to Meet in Person?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Schedule a visit to our office for a face-to-face consultation. 
              We'll show you our workspace and discuss your project over coffee!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="tel:+9779800000000"
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Call to Schedule Visit
              </a>
              <a 
                href="mailto:<EMAIL>"
                className="border border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Email for Appointment
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
