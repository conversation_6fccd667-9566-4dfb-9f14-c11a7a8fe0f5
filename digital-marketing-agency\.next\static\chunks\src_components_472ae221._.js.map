{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/projects/projects-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { ArrowRight, TrendingUp, Users, Target } from \"lucide-react\";\n\nconst achievements = [\n  {\n    icon: TrendingUp,\n    value: \"300%\",\n    label: \"Average ROI Increase\",\n    color: \"text-green-600\"\n  },\n  {\n    icon: Users,\n    value: \"2M+\",\n    label: \"People Reached\",\n    color: \"text-blue-600\"\n  },\n  {\n    icon: Target,\n    value: \"95%\",\n    label: \"Campaign Success Rate\",\n    color: \"text-purple-600\"\n  }\n];\n\nexport function ProjectsHero() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <Badge variant=\"outline\" className=\"mb-4 text-blue-600 border-blue-600\">\n              Our Work\n            </Badge>\n            \n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 leading-tight mb-6\">\n              Success Stories &{\" \"}\n              <span className=\"gradient-text\">Case Studies</span>\n            </h1>\n            \n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12\">\n              Discover how we've helped businesses across Nepal achieve remarkable growth \n              through strategic digital marketing campaigns. Each project tells a story \n              of transformation and success.\n            </p>\n\n            {/* Achievement Stats */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n              {achievements.map((achievement, index) => (\n                <motion.div\n                  key={achievement.label}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className=\"bg-white p-6 rounded-xl shadow-lg\"\n                >\n                  <achievement.icon className={`h-8 w-8 ${achievement.color} mx-auto mb-3`} />\n                  <div className={`text-3xl font-bold ${achievement.color} mb-2`}>\n                    {achievement.value}\n                  </div>\n                  <div className=\"text-gray-600\">{achievement.label}</div>\n                </motion.div>\n              ))}\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            >\n              <Button size=\"lg\" className=\"gradient-bg text-white px-8 py-4 text-lg group\">\n                View All Projects\n                <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n              </Button>\n              \n              <Button variant=\"outline\" size=\"lg\" className=\"px-8 py-4 text-lg\">\n                Start Your Success Story\n              </Button>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,eAAe;IACnB;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;QACP,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAqC;;;;;;sCAIxE,6LAAC;4BAAG,WAAU;;gCAAkE;gCAC5D;8CAClB,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAGlC,6LAAC;4BAAE,WAAU;sCAAgE;;;;;;sCAO7E,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAEV,6LAAC,YAAY,IAAI;4CAAC,WAAW,AAAC,WAA4B,OAAlB,YAAY,KAAK,EAAC;;;;;;sDAC1D,6LAAC;4CAAI,WAAW,AAAC,sBAAuC,OAAlB,YAAY,KAAK,EAAC;sDACrD,YAAY,KAAK;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;sDAAiB,YAAY,KAAK;;;;;;;mCAV5C,YAAY,KAAK;;;;;;;;;;sCAe5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;;wCAAiD;sDAE3E,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShF;KAhEgB", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/projects/projects-grid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { \n  ExternalLink, \n  TrendingUp, \n  Users, \n  Eye, \n  ShoppingCart,\n  Store,\n  Coffee,\n  Laptop,\n  Heart,\n  GraduationCap,\n  Home\n} from \"lucide-react\";\n\nconst projects = [\n  {\n    id: 1,\n    title: \"Sharma Electronics - Regional Expansion\",\n    client: \"Sharma Electronics\",\n    industry: \"Electronics\",\n    icon: Store,\n    description: \"Transformed a local electronics store into a regional brand through strategic social media marketing and targeted advertising.\",\n    challenge: \"Limited brand awareness beyond local area, low online presence, traditional marketing approach\",\n    solution: \"Comprehensive social media strategy, influencer partnerships, targeted Facebook and Google ads\",\n    results: [\n      { metric: \"Sales Increase\", value: \"300%\", icon: TrendingUp },\n      { metric: \"Social Followers\", value: \"15K+\", icon: Users },\n      { metric: \"Website Traffic\", value: \"500%\", icon: Eye }\n    ],\n    duration: \"6 months\",\n    platforms: [\"Facebook\", \"Instagram\", \"Google Ads\"],\n    category: \"retail\",\n    featured: true\n  },\n  {\n    id: 2,\n    title: \"Himalayan Cafe Chain - Brand Building\",\n    client: \"Himalayan Cafe Chain\",\n    industry: \"Food & Beverage\",\n    icon: Coffee,\n    description: \"Built a strong social media presence for a cafe chain, focusing on food photography and customer engagement.\",\n    challenge: \"New brand with no social media presence, competitive food market, need for brand differentiation\",\n    solution: \"Professional food photography, Instagram-first strategy, user-generated content campaigns\",\n    results: [\n      { metric: \"Instagram Followers\", value: \"50K+\", icon: Users },\n      { metric: \"Engagement Rate\", value: \"8.5%\", icon: Heart },\n      { metric: \"Store Visits\", value: \"200%\", icon: Eye }\n    ],\n    duration: \"8 months\",\n    platforms: [\"Instagram\", \"Facebook\", \"TikTok\"],\n    category: \"food\",\n    featured: true\n  },\n  {\n    id: 3,\n    title: \"TechStart Nepal - Lead Generation\",\n    client: \"TechStart Nepal\",\n    industry: \"Technology\",\n    icon: Laptop,\n    description: \"Generated qualified leads for a tech startup through LinkedIn marketing and Google Ads campaigns.\",\n    challenge: \"B2B lead generation, limited budget, competitive tech market\",\n    solution: \"LinkedIn advertising, content marketing, Google Ads optimization, landing page creation\",\n    results: [\n      { metric: \"Qualified Leads\", value: \"500+\", icon: Users },\n      { metric: \"Cost per Lead\", value: \"-60%\", icon: TrendingUp },\n      { metric: \"Conversion Rate\", value: \"12%\", icon: ShoppingCart }\n    ],\n    duration: \"4 months\",\n    platforms: [\"LinkedIn\", \"Google Ads\", \"Website\"],\n    category: \"technology\",\n    featured: false\n  },\n  {\n    id: 4,\n    title: \"Everest Academy - Student Enrollment\",\n    client: \"Everest Academy\",\n    industry: \"Education\",\n    icon: GraduationCap,\n    description: \"Increased student enrollment for a private academy through targeted social media campaigns and parent engagement.\",\n    challenge: \"Declining enrollment, competition from other schools, limited marketing budget\",\n    solution: \"Parent-focused Facebook campaigns, success story content, virtual school tours\",\n    results: [\n      { metric: \"Enrollment Increase\", value: \"150%\", icon: TrendingUp },\n      { metric: \"Inquiry Calls\", value: \"400%\", icon: Users },\n      { metric: \"Social Reach\", value: \"100K+\", icon: Eye }\n    ],\n    duration: \"5 months\",\n    platforms: [\"Facebook\", \"Instagram\", \"YouTube\"],\n    category: \"education\",\n    featured: false\n  },\n  {\n    id: 5,\n    title: \"Nepal Real Estate - Property Sales\",\n    client: \"Nepal Real Estate\",\n    industry: \"Real Estate\",\n    icon: Home,\n    description: \"Boosted property sales through virtual tours, targeted advertising, and lead nurturing campaigns.\",\n    challenge: \"Slow property sales, need for virtual showcasing, lead qualification\",\n    solution: \"Virtual property tours, Facebook lead ads, email marketing automation\",\n    results: [\n      { metric: \"Property Sales\", value: \"250%\", icon: TrendingUp },\n      { metric: \"Qualified Leads\", value: \"800+\", icon: Users },\n      { metric: \"Tour Requests\", value: \"300%\", icon: Eye }\n    ],\n    duration: \"7 months\",\n    platforms: [\"Facebook\", \"Instagram\", \"YouTube\", \"Email\"],\n    category: \"realestate\",\n    featured: false\n  },\n  {\n    id: 6,\n    title: \"Kathmandu Fashion Hub - E-commerce Growth\",\n    client: \"Kathmandu Fashion Hub\",\n    industry: \"Fashion\",\n    icon: Heart,\n    description: \"Grew online fashion store through influencer marketing, social commerce, and targeted advertising.\",\n    challenge: \"Low online sales, brand awareness, competition from established brands\",\n    solution: \"Influencer collaborations, Instagram shopping, Facebook dynamic ads\",\n    results: [\n      { metric: \"Online Sales\", value: \"400%\", icon: ShoppingCart },\n      { metric: \"Social Followers\", value: \"25K+\", icon: Users },\n      { metric: \"ROAS\", value: \"5.2x\", icon: TrendingUp }\n    ],\n    duration: \"6 months\",\n    platforms: [\"Instagram\", \"Facebook\", \"TikTok\"],\n    category: \"fashion\",\n    featured: true\n  }\n];\n\nconst categories = [\n  { id: \"all\", name: \"All Projects\" },\n  { id: \"retail\", name: \"Retail\" },\n  { id: \"food\", name: \"Food & Beverage\" },\n  { id: \"technology\", name: \"Technology\" },\n  { id: \"education\", name: \"Education\" },\n  { id: \"realestate\", name: \"Real Estate\" },\n  { id: \"fashion\", name: \"Fashion\" }\n];\n\nexport function ProjectsGrid() {\n  const [activeCategory, setActiveCategory] = useState(\"all\");\n  const [showAll, setShowAll] = useState(false);\n\n  const filteredProjects = projects.filter(project => \n    activeCategory === \"all\" || project.category === activeCategory\n  );\n\n  const displayedProjects = showAll ? filteredProjects : filteredProjects.slice(0, 6);\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Filter Tabs */}\n        <motion.div \n          className=\"flex flex-wrap justify-center gap-2 mb-12\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n        >\n          {categories.map((category) => (\n            <Button\n              key={category.id}\n              variant={activeCategory === category.id ? \"default\" : \"outline\"}\n              onClick={() => setActiveCategory(category.id)}\n              className={activeCategory === category.id ? \"gradient-bg text-white\" : \"\"}\n            >\n              {category.name}\n            </Button>\n          ))}\n        </motion.div>\n\n        {/* Projects Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {displayedProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full card-hover border-0 shadow-lg relative overflow-hidden group\">\n                {project.featured && (\n                  <div className=\"absolute top-4 right-4 z-10\">\n                    <Badge className=\"bg-gradient-to-r from-yellow-500 to-orange-500 text-white\">\n                      Featured\n                    </Badge>\n                  </div>\n                )}\n                \n                <CardContent className=\"p-6\">\n                  {/* Project Header */}\n                  <div className=\"flex items-start space-x-4 mb-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0\">\n                      <project.icon className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-lg font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors\">\n                        {project.title}\n                      </h3>\n                      <p className=\"text-sm text-gray-600\">{project.client} • {project.industry}</p>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  <p className=\"text-gray-600 text-sm leading-relaxed mb-4\">\n                    {project.description}\n                  </p>\n\n                  {/* Results */}\n                  <div className=\"grid grid-cols-3 gap-3 mb-4\">\n                    {project.results.map((result) => (\n                      <div key={result.metric} className=\"text-center\">\n                        <result.icon className=\"h-4 w-4 text-blue-600 mx-auto mb-1\" />\n                        <div className=\"text-lg font-bold text-gray-900\">{result.value}</div>\n                        <div className=\"text-xs text-gray-600\">{result.metric}</div>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Platforms */}\n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {project.platforms.map((platform) => (\n                      <Badge key={platform} variant=\"secondary\" className=\"text-xs\">\n                        {platform}\n                      </Badge>\n                    ))}\n                  </div>\n\n                  {/* Duration & CTA */}\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">{project.duration}</span>\n                    <Button size=\"sm\" variant=\"ghost\" className=\"text-blue-600 hover:text-blue-700\">\n                      View Details\n                      <ExternalLink className=\"ml-1 h-3 w-3\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Load More Button */}\n        {!showAll && filteredProjects.length > 6 && (\n          <motion.div \n            className=\"text-center mt-12\"\n            initial={{ opacity: 0 }}\n            whileInView={{ opacity: 1 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <Button \n              onClick={() => setShowAll(true)}\n              size=\"lg\" \n              variant=\"outline\"\n              className=\"px-8\"\n            >\n              Load More Projects\n            </Button>\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAqBA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;YACP;gBAAE,QAAQ;gBAAkB,OAAO;gBAAQ,MAAM,qNAAA,CAAA,aAAU;YAAC;YAC5D;gBAAE,QAAQ;gBAAoB,OAAO;gBAAQ,MAAM,uMAAA,CAAA,QAAK;YAAC;YACzD;gBAAE,QAAQ;gBAAmB,OAAO;gBAAQ,MAAM,mMAAA,CAAA,MAAG;YAAC;SACvD;QACD,UAAU;QACV,WAAW;YAAC;YAAY;YAAa;SAAa;QAClD,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;YACP;gBAAE,QAAQ;gBAAuB,OAAO;gBAAQ,MAAM,uMAAA,CAAA,QAAK;YAAC;YAC5D;gBAAE,QAAQ;gBAAmB,OAAO;gBAAQ,MAAM,uMAAA,CAAA,QAAK;YAAC;YACxD;gBAAE,QAAQ;gBAAgB,OAAO;gBAAQ,MAAM,mMAAA,CAAA,MAAG;YAAC;SACpD;QACD,UAAU;QACV,WAAW;YAAC;YAAa;YAAY;SAAS;QAC9C,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM,yMAAA,CAAA,SAAM;QACZ,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;YACP;gBAAE,QAAQ;gBAAmB,OAAO;gBAAQ,MAAM,uMAAA,CAAA,QAAK;YAAC;YACxD;gBAAE,QAAQ;gBAAiB,OAAO;gBAAQ,MAAM,qNAAA,CAAA,aAAU;YAAC;YAC3D;gBAAE,QAAQ;gBAAmB,OAAO;gBAAO,MAAM,yNAAA,CAAA,eAAY;YAAC;SAC/D;QACD,UAAU;QACV,WAAW;YAAC;YAAY;YAAc;SAAU;QAChD,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM,2NAAA,CAAA,gBAAa;QACnB,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;YACP;gBAAE,QAAQ;gBAAuB,OAAO;gBAAQ,MAAM,qNAAA,CAAA,aAAU;YAAC;YACjE;gBAAE,QAAQ;gBAAiB,OAAO;gBAAQ,MAAM,uMAAA,CAAA,QAAK;YAAC;YACtD;gBAAE,QAAQ;gBAAgB,OAAO;gBAAS,MAAM,mMAAA,CAAA,MAAG;YAAC;SACrD;QACD,UAAU;QACV,WAAW;YAAC;YAAY;YAAa;SAAU;QAC/C,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM,sMAAA,CAAA,OAAI;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;YACP;gBAAE,QAAQ;gBAAkB,OAAO;gBAAQ,MAAM,qNAAA,CAAA,aAAU;YAAC;YAC5D;gBAAE,QAAQ;gBAAmB,OAAO;gBAAQ,MAAM,uMAAA,CAAA,QAAK;YAAC;YACxD;gBAAE,QAAQ;gBAAiB,OAAO;gBAAQ,MAAM,mMAAA,CAAA,MAAG;YAAC;SACrD;QACD,UAAU;QACV,WAAW;YAAC;YAAY;YAAa;YAAW;SAAQ;QACxD,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,WAAW;QACX,UAAU;QACV,SAAS;YACP;gBAAE,QAAQ;gBAAgB,OAAO;gBAAQ,MAAM,yNAAA,CAAA,eAAY;YAAC;YAC5D;gBAAE,QAAQ;gBAAoB,OAAO;gBAAQ,MAAM,uMAAA,CAAA,QAAK;YAAC;YACzD;gBAAE,QAAQ;gBAAQ,OAAO;gBAAQ,MAAM,qNAAA,CAAA,aAAU;YAAC;SACnD;QACD,UAAU;QACV,WAAW;YAAC;YAAa;YAAY;SAAS;QAC9C,UAAU;QACV,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,MAAM;IAAe;IAClC;QAAE,IAAI;QAAU,MAAM;IAAS;IAC/B;QAAE,IAAI;QAAQ,MAAM;IAAkB;IACtC;QAAE,IAAI;QAAc,MAAM;IAAa;IACvC;QAAE,IAAI;QAAa,MAAM;IAAY;IACrC;QAAE,IAAI;QAAc,MAAM;IAAc;IACxC;QAAE,IAAI;QAAW,MAAM;IAAU;CAClC;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,mBAAmB,SAAS,QAAQ,QAAQ,KAAK;IAGnD,MAAM,oBAAoB,UAAU,mBAAmB,iBAAiB,KAAK,CAAC,GAAG;IAEjF,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;8BAEtB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,qIAAA,CAAA,SAAM;4BAEL,SAAS,mBAAmB,SAAS,EAAE,GAAG,YAAY;4BACtD,SAAS,IAAM,kBAAkB,SAAS,EAAE;4BAC5C,WAAW,mBAAmB,SAAS,EAAE,GAAG,2BAA2B;sCAEtE,SAAS,IAAI;2BALT,SAAS,EAAE;;;;;;;;;;8BAWtB,6LAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;oCACb,QAAQ,QAAQ,kBACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAA4D;;;;;;;;;;;kDAMjF,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;;oEAAyB,QAAQ,MAAM;oEAAC;oEAAI,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;0DAK7E,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,uBACpB,6LAAC;wDAAwB,WAAU;;0EACjC,6LAAC,OAAO,IAAI;gEAAC,WAAU;;;;;;0EACvB,6LAAC;gEAAI,WAAU;0EAAmC,OAAO,KAAK;;;;;;0EAC9D,6LAAC;gEAAI,WAAU;0EAAyB,OAAO,MAAM;;;;;;;uDAH7C,OAAO,MAAM;;;;;;;;;;0DAS3B,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,yBACtB,6LAAC,oIAAA,CAAA,QAAK;wDAAgB,SAAQ;wDAAY,WAAU;kEACjD;uDADS;;;;;;;;;;0DAOhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAyB,QAAQ,QAAQ;;;;;;kEACzD,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAQ,WAAU;;4DAAoC;0EAE9E,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA3D3B,QAAQ,EAAE;;;;;;;;;;gBAqEpB,CAAC,WAAW,iBAAiB,MAAM,GAAG,mBACrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,WAAW;wBAC1B,MAAK;wBACL,SAAQ;wBACR,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAQb;GA/HgB;KAAA", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/projects/projects-stats.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  TrendingUp, \n  Users, \n  Target, \n  Clock, \n  Award, \n  Zap,\n  BarChart3,\n  Globe\n} from \"lucide-react\";\n\nconst stats = [\n  {\n    icon: TrendingUp,\n    value: \"300%\",\n    label: \"Average ROI Increase\",\n    description: \"Our campaigns consistently deliver exceptional returns on investment\",\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    icon: Users,\n    value: \"2M+\",\n    label: \"People Reached\",\n    description: \"Total audience reached across all our marketing campaigns\",\n    color: \"from-blue-500 to-cyan-500\"\n  },\n  {\n    icon: Target,\n    value: \"95%\",\n    label: \"Campaign Success Rate\",\n    description: \"Percentage of campaigns that met or exceeded client goals\",\n    color: \"from-purple-500 to-pink-500\"\n  },\n  {\n    icon: Clock,\n    value: \"72hrs\",\n    label: \"Average Response Time\",\n    description: \"Quick turnaround time for campaign optimizations and client requests\",\n    color: \"from-orange-500 to-red-500\"\n  }\n];\n\nconst achievements = [\n  {\n    icon: Award,\n    title: \"Industry Recognition\",\n    description: \"Winner of Best Digital Marketing Agency 2023 - Nepal Business Awards\"\n  },\n  {\n    icon: Zap,\n    title: \"Rapid Growth\",\n    description: \"Helped 50+ businesses achieve 200%+ growth in their first year\"\n  },\n  {\n    icon: BarChart3,\n    title: \"Data-Driven Results\",\n    description: \"All campaigns backed by comprehensive analytics and performance tracking\"\n  },\n  {\n    icon: Globe,\n    title: \"Market Expertise\",\n    description: \"Deep understanding of Nepalese market trends and consumer behavior\"\n  }\n];\n\nconst clientTypes = [\n  { type: \"Small Businesses\", percentage: 40, count: \"20+\" },\n  { type: \"Medium Enterprises\", percentage: 35, count: \"18+\" },\n  { type: \"Large Corporations\", percentage: 15, count: \"8+\" },\n  { type: \"Startups\", percentage: 10, count: \"5+\" }\n];\n\nexport function ProjectsStats() {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-green-600 border-green-600\">\n            Our Impact\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Results That Speak for Themselves\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Numbers don't lie. Here's the measurable impact we've created for our clients \n            across various industries and campaign types.\n          </p>\n        </motion.div>\n\n        {/* Main Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full border-0 shadow-lg card-hover text-center\">\n                <CardContent className=\"p-6\">\n                  <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-full flex items-center justify-center mx-auto mb-4`}>\n                    <stat.icon className=\"h-8 w-8 text-white\" />\n                  </div>\n                  \n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">\n                    {stat.value}\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                    {stat.label}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 text-sm\">\n                    {stat.description}\n                  </p>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Achievements */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mb-16\"\n        >\n          <h3 className=\"text-3xl font-bold text-center text-gray-900 mb-8\">Key Achievements</h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {achievements.map((achievement, index) => (\n              <Card key={achievement.title} className=\"border-0 shadow-lg card-hover\">\n                <CardContent className=\"p-6 text-center\">\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                    <achievement.icon className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  \n                  <h4 className=\"font-bold text-gray-900 mb-2\">\n                    {achievement.title}\n                  </h4>\n                  \n                  <p className=\"text-gray-600 text-sm\">\n                    {achievement.description}\n                  </p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Client Distribution */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg\">\n            <h3 className=\"text-2xl font-bold text-center text-gray-900 mb-8\">\n              Our Client Portfolio\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {clientTypes.map((client, index) => (\n                <div key={client.type} className=\"text-center\">\n                  <div className=\"relative w-24 h-24 mx-auto mb-4\">\n                    <svg className=\"w-24 h-24 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                      <path\n                        className=\"text-gray-200\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"3\"\n                        fill=\"none\"\n                        d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      />\n                      <path\n                        className=\"text-blue-600\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"3\"\n                        strokeDasharray={`${client.percentage}, 100`}\n                        strokeLinecap=\"round\"\n                        fill=\"none\"\n                        d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      />\n                    </svg>\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <span className=\"text-lg font-bold text-gray-900\">{client.percentage}%</span>\n                    </div>\n                  </div>\n                  \n                  <h4 className=\"font-semibold text-gray-900 mb-1\">{client.type}</h4>\n                  <p className=\"text-gray-600 text-sm\">{client.count} clients</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBA,MAAM,QAAQ;IACZ;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,qNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM;QAAoB,YAAY;QAAI,OAAO;IAAM;IACzD;QAAE,MAAM;QAAsB,YAAY;QAAI,OAAO;IAAM;IAC3D;QAAE,MAAM;QAAsB,YAAY;QAAI,OAAO;IAAK;IAC1D;QAAE,MAAM;QAAY,YAAY;QAAI,OAAO;IAAK;CACjD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAuC;;;;;;sCAG1E,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAW,AAAC,8BAAwC,OAAX,KAAK,KAAK,EAAC;sDACvD,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAGvB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAGb,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAGb,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;;;;;2BArBlB,KAAK,KAAK;;;;;;;;;;8BA8BrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAElE,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,mIAAA,CAAA,OAAI;oCAAyB,WAAU;8CACtC,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,YAAY,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAG9B,6LAAC;gDAAG,WAAU;0DACX,YAAY,KAAK;;;;;;0DAGpB,6LAAC;gDAAE,WAAU;0DACV,YAAY,WAAW;;;;;;;;;;;;mCAXnB,YAAY,KAAK;;;;;;;;;;;;;;;;8BAoBlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC;wCAAsB,WAAU;;0DAC/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAiC,SAAQ;;0EACtD,6LAAC;gEACC,WAAU;gEACV,QAAO;gEACP,aAAY;gEACZ,MAAK;gEACL,GAAE;;;;;;0EAEJ,6LAAC;gEACC,WAAU;gEACV,QAAO;gEACP,aAAY;gEACZ,iBAAiB,AAAC,GAAoB,OAAlB,OAAO,UAAU,EAAC;gEACtC,eAAc;gEACd,MAAK;gEACL,GAAE;;;;;;;;;;;;kEAGN,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;gEAAmC,OAAO,UAAU;gEAAC;;;;;;;;;;;;;;;;;;0DAIzE,6LAAC;gDAAG,WAAU;0DAAoC,OAAO,IAAI;;;;;;0DAC7D,6LAAC;gDAAE,WAAU;;oDAAyB,OAAO,KAAK;oDAAC;;;;;;;;uCA1B3C,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCrC;KAzIgB", "debugId": null}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/cta-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight, Phone, Mail, MessageCircle } from \"lucide-react\";\n\nexport function CTASection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl\"></div>\n        <div className=\"absolute bottom-10 right-10 w-48 h-48 bg-purple-300/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-300/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"space-y-8\"\n        >\n          {/* Main Heading */}\n          <motion.h2 \n            className=\"text-4xl md:text-6xl font-bold leading-tight\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            Start Promoting Your Business{\" \"}\n            <span className=\"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n              Today\n            </span>\n          </motion.h2>\n\n          {/* Subtitle */}\n          <motion.p \n            className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            Ready to transform your business with powerful digital marketing? \n            Get a free strategy session and discover how we can help you grow.\n          </motion.p>\n\n          {/* Benefits List */}\n          <motion.div \n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 my-12\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Free Consultation</h3>\n              <p className=\"text-blue-100\">30-minute strategy session to understand your goals</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Custom Strategy</h3>\n              <p className=\"text-blue-100\">Tailored digital marketing plan for your business</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Quick Results</h3>\n              <p className=\"text-blue-100\">See improvements in your online presence within weeks</p>\n            </div>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Button \n              size=\"lg\" \n              className=\"bg-white text-purple-600 hover:bg-gray-100 font-semibold px-8 py-4 text-lg group\"\n            >\n              Request Free Strategy Session\n              <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n          </motion.div>\n\n          {/* Contact Options */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mt-8\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.0 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Phone className=\"h-5 w-5\" />\n              <span>+977-9800000000</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Mail className=\"h-5 w-5\" />\n              <span><EMAIL></span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <MessageCircle className=\"h-5 w-5\" />\n              <span>WhatsApp Available</span>\n            </div>\n          </motion.div>\n\n          {/* Urgency Element */}\n          <motion.div \n            className=\"mt-8 p-4 bg-yellow-400/20 rounded-lg border border-yellow-400/30\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n            viewport={{ once: true }}\n          >\n            <p className=\"text-yellow-100 font-medium\">\n              🎯 Limited Time: Get 20% off your first campaign when you book this month!\n            </p>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            className=\"flex flex-wrap justify-center items-center gap-8 mt-12 pt-8 border-t border-white/20\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.4 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">50+</div>\n              <div className=\"text-blue-200 text-sm\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">5+</div>\n              <div className=\"text-blue-200 text-sm\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">24/7</div>\n              <div className=\"text-blue-200 text-sm\">Support</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">100%</div>\n              <div className=\"text-blue-200 text-sm\">Satisfaction</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;gCACxB;gCAC+B;8CAC9B,6LAAC;oCAAK,WAAU;8CAA+E;;;;;;;;;;;;sCAMjG,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCACxB;;;;;;sCAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAKjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;;oCACX;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;sCAM7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD;KAnJgB", "debugId": null}}]}