import { <PERSON>ada<PERSON> } from "next";
import { TestimonialsHero } from "@/components/testimonials/testimonials-hero";
import { TestimonialsGrid } from "@/components/testimonials/testimonials-grid";
import { TestimonialsVideo } from "@/components/testimonials/testimonials-video";
import { CTASection } from "@/components/cta-section";

export const metadata: Metadata = {
  title: "Client Testimonials | Digital Marketing Success Stories Nepal",
  description: "Read what our clients say about our digital marketing services in Biratnagar, Nepal. Real testimonials from businesses that have grown with our help.",
  keywords: "client testimonials Nepal, digital marketing reviews Biratnagar, customer success stories, social media marketing feedback",
};

export default function TestimonialsPage() {
  return (
    <div className="min-h-screen">
      <TestimonialsHero />
      <TestimonialsGrid />
      <TestimonialsVideo />
      <CTASection />
    </div>
  );
}
