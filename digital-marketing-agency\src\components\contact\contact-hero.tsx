"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { Phone, Mail, MapPin, Clock } from "lucide-react";

const quickContact = [
  {
    icon: Phone,
    label: "Call Us",
    value: "+977-9800000000",
    action: "tel:+9779800000000",
    color: "text-green-600"
  },
  {
    icon: Mail,
    label: "Email Us",
    value: "<EMAIL>",
    action: "mailto:<EMAIL>",
    color: "text-blue-600"
  },
  {
    icon: MapPin,
    label: "Visit Us",
    value: "Biratnagar, Nepal",
    action: "#map",
    color: "text-purple-600"
  },
  {
    icon: Clock,
    label: "Office Hours",
    value: "Sun-Fri: 9AM-6PM",
    action: "",
    color: "text-orange-600"
  }
];

export function ContactHero() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=1920&h=1080&fit=crop&crop=center"
          alt="Contact Us - Office Building"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-purple-900/90"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Badge variant="outline" className="mb-4 text-white border-white">
              Get In Touch
            </Badge>

            <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight mb-6">
              Let's Start Your{" "}
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">Success Story</span>
            </h1>

            <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed mb-12">
              Ready to transform your business with digital marketing? We're here to help!
              Get in touch for a free consultation and discover how we can grow your brand.
            </p>

            {/* Quick Contact Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickContact.map((contact, index) => (
                <motion.div
                  key={contact.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow cursor-pointer"
                  onClick={() => contact.action && window.open(contact.action, '_self')}
                >
                  <contact.icon className={`h-8 w-8 ${contact.color} mx-auto mb-3`} />
                  <h3 className="font-semibold text-gray-900 mb-2">{contact.label}</h3>
                  <p className="text-gray-600 text-sm">{contact.value}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
