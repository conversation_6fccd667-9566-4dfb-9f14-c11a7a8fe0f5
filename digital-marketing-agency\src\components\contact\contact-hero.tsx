"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Clock } from "lucide-react";

const quickContact = [
  {
    icon: Phone,
    label: "Call Us",
    value: "+977-9800000000",
    action: "tel:+9779800000000",
    color: "text-green-600"
  },
  {
    icon: Mail,
    label: "Email Us",
    value: "<EMAIL>",
    action: "mailto:<EMAIL>",
    color: "text-blue-600"
  },
  {
    icon: MapPin,
    label: "Visit Us",
    value: "Biratnagar, Nepal",
    action: "#map",
    color: "text-purple-600"
  },
  {
    icon: Clock,
    label: "Office Hours",
    value: "Sun-Fri: 9AM-6PM",
    action: "",
    color: "text-orange-600"
  }
];

export function ContactHero() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Badge variant="outline" className="mb-4 text-blue-600 border-blue-600">
              Get In Touch
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight mb-6">
              Let's Start Your{" "}
              <span className="gradient-text">Success Story</span>
            </h1>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
              Ready to transform your business with digital marketing? We're here to help! 
              Get in touch for a free consultation and discover how we can grow your brand.
            </p>

            {/* Quick Contact Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickContact.map((contact, index) => (
                <motion.div
                  key={contact.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow cursor-pointer"
                  onClick={() => contact.action && window.open(contact.action, '_self')}
                >
                  <contact.icon className={`h-8 w-8 ${contact.color} mx-auto mb-3`} />
                  <h3 className="font-semibold text-gray-900 mb-2">{contact.label}</h3>
                  <p className="text-gray-600 text-sm">{contact.value}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
