"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  Users, 
  Target, 
  Clock, 
  Award, 
  Zap,
  BarChart3,
  Globe
} from "lucide-react";

const stats = [
  {
    icon: TrendingUp,
    value: "300%",
    label: "Average ROI Increase",
    description: "Our campaigns consistently deliver exceptional returns on investment",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Users,
    value: "2M+",
    label: "People Reached",
    description: "Total audience reached across all our marketing campaigns",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Target,
    value: "95%",
    label: "Campaign Success Rate",
    description: "Percentage of campaigns that met or exceeded client goals",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Clock,
    value: "72hrs",
    label: "Average Response Time",
    description: "Quick turnaround time for campaign optimizations and client requests",
    color: "from-orange-500 to-red-500"
  }
];

const achievements = [
  {
    icon: Award,
    title: "Industry Recognition",
    description: "Winner of Best Digital Marketing Agency 2023 - Nepal Business Awards"
  },
  {
    icon: Zap,
    title: "Rapid Growth",
    description: "Helped 50+ businesses achieve 200%+ growth in their first year"
  },
  {
    icon: BarChart3,
    title: "Data-Driven Results",
    description: "All campaigns backed by comprehensive analytics and performance tracking"
  },
  {
    icon: Globe,
    title: "Market Expertise",
    description: "Deep understanding of Nepalese market trends and consumer behavior"
  }
];

const clientTypes = [
  { type: "Small Businesses", percentage: 40, count: "20+" },
  { type: "Medium Enterprises", percentage: 35, count: "18+" },
  { type: "Large Corporations", percentage: 15, count: "8+" },
  { type: "Startups", percentage: 10, count: "5+" }
];

export function ProjectsStats() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-green-600 border-green-600">
            Our Impact
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Results That Speak for Themselves
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Numbers don't lie. Here's the measurable impact we've created for our clients 
            across various industries and campaign types.
          </p>
        </motion.div>

        {/* Main Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-0 shadow-lg card-hover text-center">
                <CardContent className="p-6">
                  <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <stat.icon className="h-8 w-8 text-white" />
                  </div>
                  
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {stat.value}
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {stat.label}
                  </h3>
                  
                  <p className="text-gray-600 text-sm">
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-8">Key Achievements</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => (
              <Card key={achievement.title} className="border-0 shadow-lg card-hover">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <achievement.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  
                  <h4 className="font-bold text-gray-900 mb-2">
                    {achievement.title}
                  </h4>
                  
                  <p className="text-gray-600 text-sm">
                    {achievement.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* Client Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
              Our Client Portfolio
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {clientTypes.map((client, index) => (
                <div key={client.type} className="text-center">
                  <div className="relative w-24 h-24 mx-auto mb-4">
                    <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        className="text-gray-200"
                        stroke="currentColor"
                        strokeWidth="3"
                        fill="none"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                      <path
                        className="text-blue-600"
                        stroke="currentColor"
                        strokeWidth="3"
                        strokeDasharray={`${client.percentage}, 100`}
                        strokeLinecap="round"
                        fill="none"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold text-gray-900">{client.percentage}%</span>
                    </div>
                  </div>
                  
                  <h4 className="font-semibold text-gray-900 mb-1">{client.type}</h4>
                  <p className="text-gray-600 text-sm">{client.count} clients</p>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
