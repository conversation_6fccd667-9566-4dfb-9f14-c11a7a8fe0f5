{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/services/services-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { ArrowRight, CheckCircle } from \"lucide-react\";\n\nconst highlights = [\n  \"Comprehensive Digital Solutions\",\n  \"ROI-Focused Campaigns\",\n  \"Local Market Expertise\",\n  \"24/7 Support & Monitoring\"\n];\n\nexport function ServicesHero() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-50 to-purple-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <Badge variant=\"outline\" className=\"mb-4 text-blue-600 border-blue-600\">\n              Our Services\n            </Badge>\n            \n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 leading-tight mb-6\">\n              Complete Digital Marketing{\" \"}\n              <span className=\"gradient-text\">Solutions</span>\n            </h1>\n            \n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8\">\n              From strategy to execution, we provide end-to-end digital marketing services \n              that drive real results for businesses across Nepal. Let us help you grow \n              your brand and reach your target audience effectively.\n            </p>\n\n            {/* Highlights */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8\">\n              {highlights.map((highlight, index) => (\n                <motion.div\n                  key={highlight}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className=\"flex items-center space-x-2 bg-white p-3 rounded-lg shadow-sm\"\n                >\n                  <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                  <span className=\"text-gray-700 font-medium text-sm\">{highlight}</span>\n                </motion.div>\n              ))}\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            >\n              <Button size=\"lg\" className=\"gradient-bg text-white px-8 py-4 text-lg group\">\n                Explore Our Services\n                <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n              </Button>\n              \n              <Button variant=\"outline\" size=\"lg\" className=\"px-8 py-4 text-lg\">\n                Get Free Consultation\n              </Button>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAqC;;;;;;sCAIxE,8OAAC;4BAAG,WAAU;;gCAAkE;gCACnD;8CAC3B,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAGlC,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;sCAO5E,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAEV,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAqC;;;;;;;mCAPhD;;;;;;;;;;sCAYX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;;wCAAiD;sDAE3E,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShF", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,8OAAC,qKAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,qKAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/services/services-accordion.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n  AccordionTrigger,\n} from \"@/components/ui/accordion\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Share2, \n  Users, \n  Camera, \n  Target,\n  BarChart3,\n  Megaphone,\n  Globe,\n  Mail,\n  CheckCircle\n} from \"lucide-react\";\n\nconst services = [\n  {\n    id: \"social-media\",\n    icon: Share2,\n    title: \"Social Media Strategy & Management\",\n    description: \"Comprehensive social media marketing across all major platforms\",\n    color: \"from-blue-500 to-cyan-500\",\n    features: [\n      \"Platform-specific content strategy\",\n      \"Daily content creation and posting\",\n      \"Community management and engagement\",\n      \"Social media advertising campaigns\",\n      \"Performance analytics and reporting\",\n      \"Competitor analysis and insights\"\n    ],\n    platforms: [\"Facebook\", \"Instagram\", \"TikTok\", \"LinkedIn\", \"Twitter\"],\n    pricing: \"Starting from NPR 25,000/month\"\n  },\n  {\n    id: \"influencer\",\n    icon: Users,\n    title: \"Influencer Marketing & Collaborations\",\n    description: \"Connect with top influencers to amplify your brand message\",\n    color: \"from-purple-500 to-pink-500\",\n    features: [\n      \"Influencer identification and outreach\",\n      \"Campaign strategy and planning\",\n      \"Contract negotiation and management\",\n      \"Content collaboration and approval\",\n      \"Performance tracking and ROI analysis\",\n      \"Long-term partnership development\"\n    ],\n    platforms: [\"Instagram\", \"TikTok\", \"YouTube\", \"Facebook\"],\n    pricing: \"Starting from NPR 15,000/campaign\"\n  },\n  {\n    id: \"content\",\n    icon: Camera,\n    title: \"Content Creation & Production\",\n    description: \"Professional content that tells your brand story effectively\",\n    color: \"from-green-500 to-emerald-500\",\n    features: [\n      \"Professional photography and videography\",\n      \"Graphic design and visual branding\",\n      \"Copywriting and content planning\",\n      \"Video editing and post-production\",\n      \"Brand guidelines development\",\n      \"Content calendar management\"\n    ],\n    platforms: [\"All Platforms\", \"Website\", \"Print Media\"],\n    pricing: \"Starting from NPR 20,000/month\"\n  },\n  {\n    id: \"advertising\",\n    icon: Target,\n    title: \"Paid Advertising & PPC\",\n    description: \"Data-driven advertising campaigns for maximum ROI\",\n    color: \"from-orange-500 to-red-500\",\n    features: [\n      \"Meta Ads (Facebook & Instagram)\",\n      \"Google Ads and Google Shopping\",\n      \"TikTok advertising campaigns\",\n      \"LinkedIn advertising for B2B\",\n      \"Campaign optimization and A/B testing\",\n      \"Conversion tracking and analytics\"\n    ],\n    platforms: [\"Meta\", \"Google\", \"TikTok\", \"LinkedIn\"],\n    pricing: \"Starting from NPR 30,000/month + ad spend\"\n  },\n  {\n    id: \"analytics\",\n    icon: BarChart3,\n    title: \"Analytics & Performance Tracking\",\n    description: \"Comprehensive reporting and data-driven insights\",\n    color: \"from-indigo-500 to-purple-500\",\n    features: [\n      \"Google Analytics setup and monitoring\",\n      \"Social media analytics and reporting\",\n      \"ROI tracking and measurement\",\n      \"Custom dashboard creation\",\n      \"Monthly performance reports\",\n      \"Strategic recommendations\"\n    ],\n    platforms: [\"Google Analytics\", \"Social Platforms\", \"Custom Tools\"],\n    pricing: \"Starting from NPR 10,000/month\"\n  },\n  {\n    id: \"branding\",\n    icon: Megaphone,\n    title: \"Brand Development & Strategy\",\n    description: \"Build a strong brand identity that resonates with your audience\",\n    color: \"from-pink-500 to-rose-500\",\n    features: [\n      \"Brand identity design and development\",\n      \"Logo design and brand guidelines\",\n      \"Brand positioning and messaging\",\n      \"Market research and analysis\",\n      \"Brand voice and tone development\",\n      \"Brand consistency across all channels\"\n    ],\n    platforms: [\"All Channels\", \"Digital & Print\"],\n    pricing: \"Starting from NPR 50,000/project\"\n  }\n];\n\nexport function ServicesAccordion() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-purple-600 border-purple-600\">\n            Detailed Services\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Everything You Need to Succeed Online\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Explore our comprehensive range of digital marketing services designed \n            to help your business thrive in the digital landscape.\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <Accordion type=\"single\" collapsible className=\"space-y-4\">\n            {services.map((service, index) => (\n              <AccordionItem \n                key={service.id} \n                value={service.id}\n                className=\"border border-gray-200 rounded-lg px-6 shadow-sm hover:shadow-md transition-shadow\"\n              >\n                <AccordionTrigger className=\"hover:no-underline py-6\">\n                  <div className=\"flex items-center space-x-4 text-left\">\n                    <div className={`p-3 rounded-lg bg-gradient-to-r ${service.color}`}>\n                      <service.icon className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"text-xl font-bold text-gray-900\">{service.title}</h3>\n                      <p className=\"text-gray-600 mt-1\">{service.description}</p>\n                    </div>\n                  </div>\n                </AccordionTrigger>\n                \n                <AccordionContent className=\"pb-6\">\n                  <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4\">\n                    {/* Features */}\n                    <div className=\"lg:col-span-2\">\n                      <h4 className=\"font-semibold text-gray-900 mb-4\">What's Included:</h4>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                        {service.features.map((feature) => (\n                          <div key={feature} className=\"flex items-start space-x-2\">\n                            <CheckCircle className=\"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0\" />\n                            <span className=\"text-gray-700\">{feature}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                    \n                    {/* Platforms & Pricing */}\n                    <div className=\"space-y-6\">\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900 mb-3\">Platforms:</h4>\n                        <div className=\"flex flex-wrap gap-2\">\n                          {service.platforms.map((platform) => (\n                            <Badge key={platform} variant=\"secondary\">\n                              {platform}\n                            </Badge>\n                          ))}\n                        </div>\n                      </div>\n                      \n                      <div>\n                        <h4 className=\"font-semibold text-gray-900 mb-2\">Pricing:</h4>\n                        <p className=\"text-blue-600 font-medium\">{service.pricing}</p>\n                      </div>\n                    </div>\n                  </div>\n                </AccordionContent>\n              </AccordionItem>\n            ))}\n          </Accordion>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;AAsBA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YAAC;YAAY;YAAa;YAAU;YAAY;SAAU;QACrE,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YAAC;YAAa;YAAU;YAAW;SAAW;QACzD,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YAAC;YAAiB;YAAW;SAAc;QACtD,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YAAC;YAAQ;YAAU;YAAU;SAAW;QACnD,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,kNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YAAC;YAAoB;YAAoB;SAAe;QACnE,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YAAC;YAAgB;SAAkB;QAC9C,SAAS;IACX;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAyC;;;;;;sCAG5E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC,qIAAA,CAAA,YAAS;wBAAC,MAAK;wBAAS,WAAW;wBAAC,WAAU;kCAC5C,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,qIAAA,CAAA,gBAAa;gCAEZ,OAAO,QAAQ,EAAE;gCACjB,WAAU;;kDAEV,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,gCAAgC,EAAE,QAAQ,KAAK,EAAE;8DAChE,cAAA,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC,QAAQ,KAAK;;;;;;sEAC9D,8OAAC;4DAAE,WAAU;sEAAsB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAK5D,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAC1B,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC;oEAAkB,WAAU;;sFAC3B,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;4EAAK,WAAU;sFAAiB;;;;;;;mEAFzB;;;;;;;;;;;;;;;;8DAShB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAI,WAAU;8EACZ,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,yBACtB,8OAAC,iIAAA,CAAA,QAAK;4EAAgB,SAAQ;sFAC3B;2EADS;;;;;;;;;;;;;;;;sEAOlB,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA6B,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA9C5D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA0D/B", "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/services/services-pricing.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { CheckCircle, Star, Zap, Crown } from \"lucide-react\";\n\nconst pricingPlans = [\n  {\n    name: \"Starter\",\n    icon: Zap,\n    price: \"25,000\",\n    period: \"month\",\n    description: \"Perfect for small businesses starting their digital journey\",\n    features: [\n      \"Social media management (2 platforms)\",\n      \"5 posts per week\",\n      \"Basic graphic design\",\n      \"Monthly analytics report\",\n      \"Email support\",\n      \"Content calendar\"\n    ],\n    popular: false,\n    color: \"from-blue-500 to-cyan-500\"\n  },\n  {\n    name: \"Professional\",\n    icon: Star,\n    price: \"45,000\",\n    period: \"month\",\n    description: \"Ideal for growing businesses looking to expand their reach\",\n    features: [\n      \"Social media management (4 platforms)\",\n      \"10 posts per week\",\n      \"Advanced graphic design & video\",\n      \"Paid advertising management\",\n      \"Bi-weekly analytics reports\",\n      \"Priority support\",\n      \"Influencer collaboration (1/month)\",\n      \"SEO optimization\"\n    ],\n    popular: true,\n    color: \"from-purple-500 to-pink-500\"\n  },\n  {\n    name: \"Enterprise\",\n    icon: Crown,\n    price: \"75,000\",\n    period: \"month\",\n    description: \"Comprehensive solution for established businesses\",\n    features: [\n      \"Full-service digital marketing\",\n      \"Unlimited social media posts\",\n      \"Professional video production\",\n      \"Multi-platform advertising\",\n      \"Weekly analytics & strategy calls\",\n      \"24/7 dedicated support\",\n      \"Multiple influencer collaborations\",\n      \"Custom landing pages\",\n      \"Advanced automation\",\n      \"Brand development\"\n    ],\n    popular: false,\n    color: \"from-orange-500 to-red-500\"\n  }\n];\n\nconst addOns = [\n  {\n    name: \"Professional Photography\",\n    price: \"15,000\",\n    description: \"High-quality product and lifestyle photography\"\n  },\n  {\n    name: \"Video Production\",\n    price: \"25,000\",\n    description: \"Professional video content creation and editing\"\n  },\n  {\n    name: \"Website Development\",\n    price: \"50,000\",\n    description: \"Custom website design and development\"\n  },\n  {\n    name: \"E-commerce Setup\",\n    price: \"35,000\",\n    description: \"Complete online store setup and optimization\"\n  }\n];\n\nexport function ServicesPricing() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-orange-600 border-orange-600\">\n            Pricing Plans\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Choose Your Growth Plan\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Transparent pricing with no hidden fees. All plans include our core services \n            with different levels of support and features.\n          </p>\n        </motion.div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n          {pricingPlans.map((plan, index) => (\n            <motion.div\n              key={plan.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <Badge className=\"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1\">\n                    Most Popular\n                  </Badge>\n                </div>\n              )}\n              \n              <Card className={`h-full border-2 ${plan.popular ? 'border-purple-500 shadow-xl' : 'border-gray-200 shadow-lg'} card-hover`}>\n                <CardContent className=\"p-8\">\n                  {/* Plan Header */}\n                  <div className=\"text-center mb-6\">\n                    <div className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center mx-auto mb-4`}>\n                      <plan.icon className=\"h-8 w-8 text-white\" />\n                    </div>\n                    \n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{plan.name}</h3>\n                    <p className=\"text-gray-600 mb-4\">{plan.description}</p>\n                    \n                    <div className=\"flex items-baseline justify-center\">\n                      <span className=\"text-sm text-gray-500\">NPR</span>\n                      <span className=\"text-4xl font-bold text-gray-900 mx-1\">{plan.price}</span>\n                      <span className=\"text-gray-500\">/{plan.period}</span>\n                    </div>\n                  </div>\n\n                  {/* Features */}\n                  <div className=\"space-y-3 mb-8\">\n                    {plan.features.map((feature) => (\n                      <div key={feature} className=\"flex items-start space-x-3\">\n                        <CheckCircle className=\"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0\" />\n                        <span className=\"text-gray-700\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* CTA Button */}\n                  <Button \n                    className={`w-full ${plan.popular \n                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600' \n                      : 'bg-gray-900 hover:bg-gray-800'\n                    } text-white`}\n                    size=\"lg\"\n                  >\n                    Get Started\n                  </Button>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Add-ons */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <h3 className=\"text-3xl font-bold text-center text-gray-900 mb-8\">Add-on Services</h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {addOns.map((addon, index) => (\n              <Card key={addon.name} className=\"border-0 shadow-lg card-hover\">\n                <CardContent className=\"p-6 text-center\">\n                  <h4 className=\"font-bold text-gray-900 mb-2\">{addon.name}</h4>\n                  <p className=\"text-gray-600 text-sm mb-4\">{addon.description}</p>\n                  <div className=\"text-2xl font-bold text-blue-600\">\n                    NPR {addon.price}\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Custom Solutions */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Need a Custom Solution?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Every business is unique. If our standard plans don't fit your needs, \n              let's create a custom package tailored specifically for your goals and budget.\n            </p>\n            <Button size=\"lg\" className=\"gradient-bg text-white\">\n              Contact for Custom Quote\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;QACT,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;QACT,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;QACT,OAAO;IACT;CACD;AAED,MAAM,SAAS;IACb;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAyC;;;;;;sCAG5E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCAET,KAAK,OAAO,kBACX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAoE;;;;;;;;;;;8CAMzF,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAW,CAAC,gBAAgB,EAAE,KAAK,OAAO,GAAG,gCAAgC,4BAA4B,WAAW,CAAC;8CACzH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,2DAA2D,CAAC;kEACnH,cAAA,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAGvB,8OAAC;wDAAG,WAAU;kEAAyC,KAAK,IAAI;;;;;;kEAChE,8OAAC;wDAAE,WAAU;kEAAsB,KAAK,WAAW;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEAAK,WAAU;0EAAyC,KAAK,KAAK;;;;;;0EACnE,8OAAC;gEAAK,WAAU;;oEAAgB;oEAAE,KAAK,MAAM;;;;;;;;;;;;;;;;;;;0DAKjD,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAFzB;;;;;;;;;;0DAQd,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAW,CAAC,OAAO,EAAE,KAAK,OAAO,GAC7B,yFACA,gCACH,WAAW,CAAC;gDACb,MAAK;0DACN;;;;;;;;;;;;;;;;;;2BAlDA,KAAK,IAAI;;;;;;;;;;8BA4DpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAElE,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,gIAAA,CAAA,OAAI;oCAAkB,WAAU;8CAC/B,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAgC,MAAM,IAAI;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAA8B,MAAM,WAAW;;;;;;0DAC5D,8OAAC;gDAAI,WAAU;;oDAAmC;oDAC3C,MAAM,KAAK;;;;;;;;;;;;;mCALX,MAAM,IAAI;;;;;;;;;;;;;;;;8BAc3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjE", "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/services/services-process.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  MessageSquare, \n  Search, \n  PenTool, \n  Rocket, \n  BarChart3, \n  RefreshCw \n} from \"lucide-react\";\n\nconst processSteps = [\n  {\n    step: \"01\",\n    icon: MessageSquare,\n    title: \"Discovery & Consultation\",\n    description: \"We start with a comprehensive consultation to understand your business goals, target audience, and current digital presence.\",\n    details: [\n      \"Business goals assessment\",\n      \"Target audience analysis\",\n      \"Competitor research\",\n      \"Current digital audit\"\n    ],\n    color: \"from-blue-500 to-cyan-500\"\n  },\n  {\n    step: \"02\",\n    icon: Search,\n    title: \"Strategy Development\",\n    description: \"Based on our findings, we develop a customized digital marketing strategy tailored to your specific needs and objectives.\",\n    details: [\n      \"Custom strategy creation\",\n      \"Platform selection\",\n      \"Content planning\",\n      \"Timeline development\"\n    ],\n    color: \"from-purple-500 to-pink-500\"\n  },\n  {\n    step: \"03\",\n    icon: PenTool,\n    title: \"Content Creation\",\n    description: \"Our creative team produces high-quality content including graphics, videos, copy, and other materials needed for your campaigns.\",\n    details: [\n      \"Visual content design\",\n      \"Video production\",\n      \"Copywriting\",\n      \"Brand asset creation\"\n    ],\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    step: \"04\",\n    icon: Rocket,\n    title: \"Campaign Launch\",\n    description: \"We execute your digital marketing campaigns across selected platforms with careful monitoring and real-time adjustments.\",\n    details: [\n      \"Campaign setup\",\n      \"Content publishing\",\n      \"Ad campaign launch\",\n      \"Initial monitoring\"\n    ],\n    color: \"from-orange-500 to-red-500\"\n  },\n  {\n    step: \"05\",\n    icon: BarChart3,\n    title: \"Monitor & Analyze\",\n    description: \"Continuous monitoring of campaign performance with detailed analytics and insights to track progress toward your goals.\",\n    details: [\n      \"Performance tracking\",\n      \"Analytics reporting\",\n      \"ROI measurement\",\n      \"Insight generation\"\n    ],\n    color: \"from-indigo-500 to-purple-500\"\n  },\n  {\n    step: \"06\",\n    icon: RefreshCw,\n    title: \"Optimize & Scale\",\n    description: \"Based on performance data, we continuously optimize campaigns and scale successful strategies for maximum impact.\",\n    details: [\n      \"Campaign optimization\",\n      \"Strategy refinement\",\n      \"Scaling successful tactics\",\n      \"Continuous improvement\"\n    ],\n    color: \"from-pink-500 to-rose-500\"\n  }\n];\n\nexport function ServicesProcess() {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-green-600 border-green-600\">\n            Our Process\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            How We Work With You\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Our proven 6-step process ensures that every project is executed with precision, \n            creativity, and a focus on delivering measurable results.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {processSteps.map((step, index) => (\n            <motion.div\n              key={step.step}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full card-hover border-0 shadow-lg relative overflow-hidden\">\n                <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${step.color} rounded-bl-full opacity-10`}></div>\n                \n                <CardContent className=\"p-6 relative\">\n                  {/* Step Number */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-lg flex items-center justify-center`}>\n                      <step.icon className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <Badge variant=\"outline\" className=\"text-gray-500\">\n                      {step.step}\n                    </Badge>\n                  </div>\n\n                  {/* Title & Description */}\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                    {step.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                    {step.description}\n                  </p>\n\n                  {/* Details */}\n                  <div className=\"space-y-2\">\n                    <h4 className=\"font-semibold text-gray-900 text-sm\">Key Activities:</h4>\n                    <ul className=\"space-y-1\">\n                      {step.details.map((detail) => (\n                        <li key={detail} className=\"flex items-center space-x-2\">\n                          <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\n                          <span className=\"text-sm text-gray-600\">{detail}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Process Timeline */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">Typical Project Timeline</h3>\n          \n          <div className=\"bg-white rounded-2xl p-8 shadow-lg\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600 mb-2\">Week 1</div>\n                <div className=\"text-gray-600\">Discovery & Strategy</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600 mb-2\">Week 2</div>\n                <div className=\"text-gray-600\">Content Creation</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600 mb-2\">Week 3</div>\n                <div className=\"text-gray-600\">Campaign Launch</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-orange-600 mb-2\">Ongoing</div>\n                <div className=\"text-gray-600\">Monitor & Optimize</div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,4MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAuC;;;;;;sCAG1E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAW,CAAC,mDAAmD,EAAE,KAAK,KAAK,CAAC,2BAA2B,CAAC;;;;;;kDAE7G,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,4CAA4C,CAAC;kEACpG,cAAA,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,KAAK,IAAI;;;;;;;;;;;;0DAKd,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAGb,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAInB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAG,WAAU;kEACX,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,uBACjB,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAK,WAAU;kFAAyB;;;;;;;+DAFlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAlCd,KAAK,IAAI;;;;;;;;;;8BAgDpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/cta-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight, Phone, Mail, MessageCircle } from \"lucide-react\";\n\nexport function CTASection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl\"></div>\n        <div className=\"absolute bottom-10 right-10 w-48 h-48 bg-purple-300/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-300/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"space-y-8\"\n        >\n          {/* Main Heading */}\n          <motion.h2 \n            className=\"text-4xl md:text-6xl font-bold leading-tight\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            Start Promoting Your Business{\" \"}\n            <span className=\"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n              Today\n            </span>\n          </motion.h2>\n\n          {/* Subtitle */}\n          <motion.p \n            className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            Ready to transform your business with powerful digital marketing? \n            Get a free strategy session and discover how we can help you grow.\n          </motion.p>\n\n          {/* Benefits List */}\n          <motion.div \n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 my-12\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Free Consultation</h3>\n              <p className=\"text-blue-100\">30-minute strategy session to understand your goals</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Custom Strategy</h3>\n              <p className=\"text-blue-100\">Tailored digital marketing plan for your business</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Quick Results</h3>\n              <p className=\"text-blue-100\">See improvements in your online presence within weeks</p>\n            </div>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Button \n              size=\"lg\" \n              className=\"bg-white text-purple-600 hover:bg-gray-100 font-semibold px-8 py-4 text-lg group\"\n            >\n              Request Free Strategy Session\n              <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n          </motion.div>\n\n          {/* Contact Options */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mt-8\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.0 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Phone className=\"h-5 w-5\" />\n              <span>+977-9800000000</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Mail className=\"h-5 w-5\" />\n              <span><EMAIL></span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <MessageCircle className=\"h-5 w-5\" />\n              <span>WhatsApp Available</span>\n            </div>\n          </motion.div>\n\n          {/* Urgency Element */}\n          <motion.div \n            className=\"mt-8 p-4 bg-yellow-400/20 rounded-lg border border-yellow-400/30\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n            viewport={{ once: true }}\n          >\n            <p className=\"text-yellow-100 font-medium\">\n              🎯 Limited Time: Get 20% off your first campaign when you book this month!\n            </p>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            className=\"flex flex-wrap justify-center items-center gap-8 mt-12 pt-8 border-t border-white/20\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.4 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">50+</div>\n              <div className=\"text-blue-200 text-sm\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">5+</div>\n              <div className=\"text-blue-200 text-sm\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">24/7</div>\n              <div className=\"text-blue-200 text-sm\">Support</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">100%</div>\n              <div className=\"text-blue-200 text-sm\">Satisfaction</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;gCACxB;gCAC+B;8CAC9B,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;;;;;;;sCAMjG,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCACxB;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAKjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;sCAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD", "debugId": null}}]}