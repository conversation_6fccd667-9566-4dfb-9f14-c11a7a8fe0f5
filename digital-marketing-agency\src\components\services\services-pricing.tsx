"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, Star, Zap, Crown } from "lucide-react";

const pricingPlans = [
  {
    name: "Starter",
    icon: Zap,
    price: "25,000",
    period: "month",
    description: "Perfect for small businesses starting their digital journey",
    features: [
      "Social media management (2 platforms)",
      "5 posts per week",
      "Basic graphic design",
      "Monthly analytics report",
      "Email support",
      "Content calendar"
    ],
    popular: false,
    color: "from-blue-500 to-cyan-500"
  },
  {
    name: "Professional",
    icon: Star,
    price: "45,000",
    period: "month",
    description: "Ideal for growing businesses looking to expand their reach",
    features: [
      "Social media management (4 platforms)",
      "10 posts per week",
      "Advanced graphic design & video",
      "Paid advertising management",
      "Bi-weekly analytics reports",
      "Priority support",
      "Influencer collaboration (1/month)",
      "SEO optimization"
    ],
    popular: true,
    color: "from-purple-500 to-pink-500"
  },
  {
    name: "Enterprise",
    icon: Crown,
    price: "75,000",
    period: "month",
    description: "Comprehensive solution for established businesses",
    features: [
      "Full-service digital marketing",
      "Unlimited social media posts",
      "Professional video production",
      "Multi-platform advertising",
      "Weekly analytics & strategy calls",
      "24/7 dedicated support",
      "Multiple influencer collaborations",
      "Custom landing pages",
      "Advanced automation",
      "Brand development"
    ],
    popular: false,
    color: "from-orange-500 to-red-500"
  }
];

const addOns = [
  {
    name: "Professional Photography",
    price: "15,000",
    description: "High-quality product and lifestyle photography"
  },
  {
    name: "Video Production",
    price: "25,000",
    description: "Professional video content creation and editing"
  },
  {
    name: "Website Development",
    price: "50,000",
    description: "Custom website design and development"
  },
  {
    name: "E-commerce Setup",
    price: "35,000",
    description: "Complete online store setup and optimization"
  }
];

export function ServicesPricing() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-orange-600 border-orange-600">
            Pricing Plans
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Choose Your Growth Plan
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Transparent pricing with no hidden fees. All plans include our core services 
            with different levels of support and features.
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="relative"
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1">
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <Card className={`h-full border-2 ${plan.popular ? 'border-purple-500 shadow-xl' : 'border-gray-200 shadow-lg'} card-hover`}>
                <CardContent className="p-8">
                  {/* Plan Header */}
                  <div className="text-center mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                      <plan.icon className="h-8 w-8 text-white" />
                    </div>
                    
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                    <p className="text-gray-600 mb-4">{plan.description}</p>
                    
                    <div className="flex items-baseline justify-center">
                      <span className="text-sm text-gray-500">NPR</span>
                      <span className="text-4xl font-bold text-gray-900 mx-1">{plan.price}</span>
                      <span className="text-gray-500">/{plan.period}</span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    {plan.features.map((feature) => (
                      <div key={feature} className="flex items-start space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <Button 
                    className={`w-full ${plan.popular 
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600' 
                      : 'bg-gray-900 hover:bg-gray-800'
                    } text-white`}
                    size="lg"
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Add-ons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-8">Add-on Services</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {addOns.map((addon, index) => (
              <Card key={addon.name} className="border-0 shadow-lg card-hover">
                <CardContent className="p-6 text-center">
                  <h4 className="font-bold text-gray-900 mb-2">{addon.name}</h4>
                  <p className="text-gray-600 text-sm mb-4">{addon.description}</p>
                  <div className="text-2xl font-bold text-blue-600">
                    NPR {addon.price}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* Custom Solutions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Need a Custom Solution?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Every business is unique. If our standard plans don't fit your needs, 
              let's create a custom package tailored specifically for your goals and budget.
            </p>
            <Button size="lg" className="gradient-bg text-white">
              Contact for Custom Quote
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
