{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowR<PERSON>, Play } from \"lucide-react\";\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background with gradient */}\n      <div className=\"absolute inset-0 hero-gradient\"></div>\n      \n      {/* Animated background elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-float\"></div>\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-purple-300/20 rounded-full blur-3xl animate-float\" style={{ animationDelay: \"2s\" }}></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-300/20 rounded-full blur-3xl animate-float\" style={{ animationDelay: \"4s\" }}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Main heading */}\n          <motion.h1 \n            className=\"text-4xl md:text-6xl lg:text-7xl font-bold leading-tight\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            Grow Your Brand with the{\" \"}\n            <span className=\"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n              Power of Social\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p \n            className=\"text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            Leading digital marketing agency in Biratnagar, Nepal. We transform businesses through strategic social media marketing, compelling content creation, and innovative online branding.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n          >\n            <Button \n              size=\"lg\" \n              className=\"bg-white text-purple-600 hover:bg-gray-100 font-semibold px-8 py-4 text-lg group\"\n            >\n              Start Your Journey\n              <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n            \n            <Button \n              variant=\"outline\" \n              size=\"lg\" \n              className=\"border-white text-white hover:bg-white hover:text-purple-600 font-semibold px-8 py-4 text-lg group\"\n            >\n              <Play className=\"mr-2 h-5 w-5\" />\n              Watch Our Story\n            </Button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div \n            className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-white/20\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold\">50+</div>\n              <div className=\"text-gray-300 mt-2\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold\">200+</div>\n              <div className=\"text-gray-300 mt-2\">Projects Completed</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold\">5+</div>\n              <div className=\"text-gray-300 mt-2\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold\">24/7</div>\n              <div className=\"text-gray-300 mt-2\">Support</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll indicator */}\n      <motion.div \n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        animate={{ y: [0, 10, 0] }}\n        transition={{ duration: 2, repeat: Infinity }}\n      >\n        <div className=\"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white/70 rounded-full mt-2\"></div>\n        </div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA6F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCAC1I,8OAAC;wBAAI,WAAU;wBAAqI,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAGpL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;gCACzC;gCAC0B;8CACzB,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;;;;;;;sCAMjG,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCACzC;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;gBAAC;gBACzB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;gBAAS;0BAE5C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/services-overview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Share2, \n  Users, \n  Camera, \n  Target,\n  TrendingUp,\n  Megaphone\n} from \"lucide-react\";\n\nconst services = [\n  {\n    icon: Share2,\n    title: \"Social Media Marketing\",\n    description: \"Strategic social media campaigns across Facebook, Instagram, TikTok, and LinkedIn to boost your brand presence and engagement.\",\n    features: [\"Content Strategy\", \"Community Management\", \"Social Analytics\", \"Brand Monitoring\"],\n    color: \"from-blue-500 to-cyan-500\"\n  },\n  {\n    icon: Users,\n    title: \"Influencer Promotions\",\n    description: \"Connect with top influencers in Nepal to amplify your brand message and reach targeted audiences authentically.\",\n    features: [\"Influencer Matching\", \"Campaign Management\", \"Performance Tracking\", \"ROI Analysis\"],\n    color: \"from-purple-500 to-pink-500\"\n  },\n  {\n    icon: Camera,\n    title: \"Content Creation\",\n    description: \"Professional photos, engaging reels, stunning graphics, and compelling copy that tells your brand story effectively.\",\n    features: [\"Photography\", \"Video Production\", \"Graphic Design\", \"Copywriting\"],\n    color: \"from-green-500 to-emerald-500\"\n  },\n  {\n    icon: Target,\n    title: \"Paid Advertising\",\n    description: \"Data-driven advertising campaigns on Meta, TikTok, and Google to maximize your ROI and drive conversions.\",\n    features: [\"Meta Ads\", \"Google Ads\", \"TikTok Ads\", \"Campaign Optimization\"],\n    color: \"from-orange-500 to-red-500\"\n  }\n];\n\nexport function ServicesOverview() {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-blue-600 border-blue-600\">\n            Our Core Services\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Comprehensive Digital Solutions\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            From strategy to execution, we provide end-to-end digital marketing services \n            that drive real results for businesses across Nepal.\n          </p>\n        </motion.div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          {services.map((service, index) => (\n            <motion.div\n              key={service.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full card-hover border-0 shadow-lg\">\n                <CardContent className=\"p-8\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className={`p-3 rounded-xl bg-gradient-to-r ${service.color}`}>\n                      <service.icon className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">\n                        {service.title}\n                      </h3>\n                      <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                        {service.description}\n                      </p>\n                      <div className=\"space-y-2\">\n                        <h4 className=\"font-semibold text-gray-900 mb-3\">Key Features:</h4>\n                        <div className=\"grid grid-cols-2 gap-2\">\n                          {service.features.map((feature) => (\n                            <div key={feature} className=\"flex items-center space-x-2\">\n                              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                              <span className=\"text-sm text-gray-600\">{feature}</span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <motion.div \n          className=\"text-center mt-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\">\n            <div className=\"flex items-center justify-center mb-4\">\n              <TrendingUp className=\"h-8 w-8 mr-3\" />\n              <h3 className=\"text-2xl font-bold\">Ready to Grow Your Business?</h3>\n            </div>\n            <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n              Let's discuss how our digital marketing expertise can help your business \n              reach new heights in the Nepalese market.\n            </p>\n            <motion.button \n              className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              Get Free Consultation\n            </motion.button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcA,MAAM,WAAW;IACf;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAoB;YAAwB;YAAoB;SAAmB;QAC9F,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAuB;YAAuB;YAAwB;SAAe;QAChG,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAe;YAAoB;YAAkB;SAAc;QAC9E,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAY;YAAc;YAAc;SAAwB;QAC3E,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAqC;;;;;;sCAGxE,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,gCAAgC,EAAE,QAAQ,KAAK,EAAE;0DAChE,cAAA,8OAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAE1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC;wEAAkB,WAAU;;0FAC3B,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAAyB;;;;;;;uEAFjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAvBnB,QAAQ,KAAK;;;;;;;;;;8BAuCxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;;;;;;;0CAErC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/trusted-partners.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Badge } from \"@/components/ui/badge\";\n\nimport { Facebook, Chrome, Music, Palette, Mail, ShoppingCart, FileText, Heart } from \"lucide-react\";\n\nconst partners = [\n  { name: \"Meta\", icon: Facebook, description: \"Official Meta Business Partner\", color: \"text-blue-600\" },\n  { name: \"Google\", icon: Chrome, description: \"Google Ads Certified Partner\", color: \"text-red-600\" },\n  { name: \"TikTok\", icon: Music, description: \"TikTok for Business Partner\", color: \"text-black\" },\n  { name: \"<PERSON>va\", icon: Palette, description: \"Canva Pro Design Partner\", color: \"text-purple-600\" },\n  { name: \"Mailchimp\", icon: Mail, description: \"Email Marketing Partner\", color: \"text-yellow-600\" },\n  { name: \"Shopify\", icon: ShoppingCart, description: \"E-commerce Solutions\", color: \"text-green-600\" },\n  { name: \"WordPress\", icon: FileText, description: \"Website Development\", color: \"text-blue-800\" },\n  { name: \"HubS<PERSON>\", icon: Heart, description: \"CRM & Marketing Automation\", color: \"text-orange-600\" },\n];\n\nconst localClients = [\n  \"Biratnagar Chamber of Commerce\",\n  \"Nepal Tourism Board\",\n  \"Local Restaurants & Cafes\",\n  \"Fashion Boutiques\",\n  \"Educational Institutions\",\n  \"Healthcare Centers\",\n  \"Real Estate Companies\",\n  \"Tech Startups\"\n];\n\nexport function TrustedPartners() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-purple-600 border-purple-600\">\n            Trusted Partners\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Partnered with Industry Leaders\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            We collaborate with top platforms and tools to deliver exceptional results for our clients.\n          </p>\n        </motion.div>\n\n        {/* Platform Partners */}\n        <motion.div \n          className=\"mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <h3 className=\"text-2xl font-bold text-center text-gray-900 mb-8\">Platform Partners</h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6\">\n            {partners.map((partner, index) => (\n              <motion.div\n                key={partner.name}\n                className=\"bg-gray-50 rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1\"\n                initial={{ opacity: 0, scale: 0.9 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ scale: 1.05 }}\n              >\n                <partner.icon className={`h-12 w-12 mb-3 mx-auto ${partner.color}`} />\n                <h4 className=\"font-bold text-gray-900 mb-2\">{partner.name}</h4>\n                <p className=\"text-sm text-gray-600\">{partner.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Client Industries */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <h3 className=\"text-2xl font-bold text-center text-gray-900 mb-8\">Industries We Serve</h3>\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {localClients.map((client, index) => (\n                <motion.div\n                  key={client}\n                  className=\"bg-white rounded-lg p-4 text-center shadow-sm\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.05 }}\n                  viewport={{ once: true }}\n                >\n                  <p className=\"text-gray-700 font-medium\">{client}</p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Scrolling Logos Animation */}\n        <motion.div \n          className=\"mt-16 overflow-hidden\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex space-x-8 animate-marquee\">\n            {[...partners, ...partners].map((partner, index) => (\n              <div key={index} className=\"flex items-center space-x-2 whitespace-nowrap\">\n                <partner.icon className={`h-6 w-6 ${partner.color}`} />\n                <span className=\"font-semibold text-gray-700\">{partner.name}</span>\n              </div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Trust Indicators */}\n        <motion.div \n          className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl font-bold text-blue-600\">100%</div>\n            <div className=\"text-gray-600\">Client Satisfaction</div>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl font-bold text-purple-600\">5+</div>\n            <div className=\"text-gray-600\">Years of Partnership</div>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"text-3xl font-bold text-green-600\">24/7</div>\n            <div className=\"text-gray-600\">Platform Support</div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM,0MAAA,CAAA,WAAQ;QAAE,aAAa;QAAkC,OAAO;IAAgB;IACtG;QAAE,MAAM;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,aAAa;QAAgC,OAAO;IAAe;IACnG;QAAE,MAAM;QAAU,MAAM,oMAAA,CAAA,QAAK;QAAE,aAAa;QAA+B,OAAO;IAAa;IAC/F;QAAE,MAAM;QAAS,MAAM,wMAAA,CAAA,UAAO;QAAE,aAAa;QAA4B,OAAO;IAAkB;IAClG;QAAE,MAAM;QAAa,MAAM,kMAAA,CAAA,OAAI;QAAE,aAAa;QAA2B,OAAO;IAAkB;IAClG;QAAE,MAAM;QAAW,MAAM,sNAAA,CAAA,eAAY;QAAE,aAAa;QAAwB,OAAO;IAAiB;IACpG;QAAE,MAAM;QAAa,MAAM,8MAAA,CAAA,WAAQ;QAAE,aAAa;QAAuB,OAAO;IAAgB;IAChG;QAAE,MAAM;QAAW,MAAM,oMAAA,CAAA,QAAK;QAAE,aAAa;QAA8B,OAAO;IAAkB;CACrG;AAED,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAyC;;;;;;sCAG5E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAClE,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,OAAO;oCAAK;;sDAE1B,8OAAC,QAAQ,IAAI;4CAAC,WAAW,CAAC,uBAAuB,EAAE,QAAQ,KAAK,EAAE;;;;;;sDAClE,8OAAC;4CAAG,WAAU;sDAAgC,QAAQ,IAAI;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAAyB,QAAQ,WAAW;;;;;;;mCAVpD,QAAQ,IAAI;;;;;;;;;;;;;;;;8BAiBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAClE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAK;wCACjD,UAAU;4CAAE,MAAM;wCAAK;kDAEvB,cAAA,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;uCAPrC;;;;;;;;;;;;;;;;;;;;;8BAef,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;kCACZ;+BAAI;+BAAa;yBAAS,CAAC,GAAG,CAAC,CAAC,SAAS,sBACxC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC,QAAQ,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;kDACnD,8OAAC;wCAAK,WAAU;kDAA+B,QAAQ,IAAI;;;;;;;+BAFnD;;;;;;;;;;;;;;;8BAShB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAClD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/testimonials-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Star, Quote, User, Briefcase, Laptop } from \"lucide-react\";\n\nconst testimonials = [\n  {\n    name: \"<PERSON><PERSON>\",\n    role: \"Owner\",\n    company: \"Sharma Electronics\",\n    icon: User,\n    rating: 5,\n    text: \"DigitalBoost transformed our local electronics store into a regional brand. Our Facebook page went from 200 to 15,000 followers in just 6 months, and sales increased by 300%!\",\n    result: \"300% Sales Increase\"\n  },\n  {\n    name: \"<PERSON><PERSON> Thapa\",\n    role: \"Marketing Manager\",\n    company: \"Himalayan Cafe Chain\",\n    icon: Briefcase,\n    rating: 5,\n    text: \"Their content creation is exceptional! The food photography and Instagram reels they created for our cafe chain helped us become the most followed restaurant in Biratnagar.\",\n    result: \"50K+ Instagram Followers\"\n  },\n  {\n    name: \"Amit <PERSON>\",\n    role: \"Founder\",\n    company: \"TechStart Nepal\",\n    icon: Laptop,\n    rating: 5,\n    text: \"Professional, creative, and results-driven. DigitalBoost's Google Ads campaigns helped us acquire 500+ new customers in our first quarter. Highly recommended!\",\n    result: \"500+ New Customers\"\n  }\n];\n\nexport function TestimonialsSection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div \n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <Badge variant=\"outline\" className=\"mb-4 text-green-600 border-green-600\">\n            Client Success Stories\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            What Our Clients Say\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Don't just take our word for it. Here's what businesses across Nepal say about working with us.\n          </p>\n        </motion.div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n          {testimonials.map((testimonial, index) => (\n            <motion.div\n              key={testimonial.name}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"h-full card-hover border-0 shadow-lg relative overflow-hidden\">\n                <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-bl-full opacity-10\"></div>\n                <CardContent className=\"p-8 relative\">\n                  <Quote className=\"h-8 w-8 text-blue-500 mb-4\" />\n                  \n                  {/* Rating */}\n                  <div className=\"flex items-center mb-4\">\n                    {[...Array(testimonial.rating)].map((_, i) => (\n                      <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n                    ))}\n                  </div>\n\n                  {/* Testimonial Text */}\n                  <p className=\"text-gray-700 mb-6 leading-relaxed italic\">\n                    \"{testimonial.text}\"\n                  </p>\n\n                  {/* Result Badge */}\n                  <Badge className=\"mb-6 bg-green-100 text-green-800 hover:bg-green-100\">\n                    {testimonial.result}\n                  </Badge>\n\n                  {/* Client Info */}\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                      <testimonial.icon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-bold text-gray-900\">{testimonial.name}</h4>\n                      <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                      <p className=\"text-sm font-medium text-blue-600\">{testimonial.company}</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <motion.div \n          className=\"bg-white rounded-2xl p-8 shadow-lg\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl md:text-4xl font-bold text-blue-600\">98%</div>\n              <div className=\"text-gray-600\">Client Retention Rate</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl md:text-4xl font-bold text-purple-600\">4.9/5</div>\n              <div className=\"text-gray-600\">Average Rating</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl md:text-4xl font-bold text-green-600\">250%</div>\n              <div className=\"text-gray-600\">Average ROI</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl md:text-4xl font-bold text-orange-600\">72hrs</div>\n              <div className=\"text-gray-600\">Response Time</div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* CTA */}\n        <motion.div \n          className=\"text-center mt-12\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Ready to Join Our Success Stories?\n          </h3>\n          <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n            Let's create your success story together. Get in touch for a free consultation.\n          </p>\n          <motion.button \n            className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Start Your Success Story\n          </motion.button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM;QACN,SAAS;QACT,MAAM,kMAAA,CAAA,OAAI;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,SAAS;QACT,MAAM,4MAAA,CAAA,YAAS;QACf,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,SAAS;QACT,MAAM,sMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAAuC;;;;;;sCAG1E,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM,YAAY,MAAM;iDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAKf,8OAAC;gDAAE,WAAU;;oDAA4C;oDACrD,YAAY,IAAI;oDAAC;;;;;;;0DAIrB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DACd,YAAY,MAAM;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,YAAY,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE9B,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA2B,YAAY,IAAI;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAyB,YAAY,IAAI;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EAAqC,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BApCxE,YAAY,IAAI;;;;;;;;;;8BA8C3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA+C;;;;;;kDAC9D,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAChE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgD;;;;;;kDAC/D,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAChE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAGpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCACzB;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1775, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/cta-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight, Phone, Mail, MessageCircle } from \"lucide-react\";\n\nexport function CTASection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl\"></div>\n        <div className=\"absolute bottom-10 right-10 w-48 h-48 bg-purple-300/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-blue-300/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"space-y-8\"\n        >\n          {/* Main Heading */}\n          <motion.h2 \n            className=\"text-4xl md:text-6xl font-bold leading-tight\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            Start Promoting Your Business{\" \"}\n            <span className=\"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n              Today\n            </span>\n          </motion.h2>\n\n          {/* Subtitle */}\n          <motion.p \n            className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            Ready to transform your business with powerful digital marketing? \n            Get a free strategy session and discover how we can help you grow.\n          </motion.p>\n\n          {/* Benefits List */}\n          <motion.div \n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 my-12\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Free Consultation</h3>\n              <p className=\"text-blue-100\">30-minute strategy session to understand your goals</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Custom Strategy</h3>\n              <p className=\"text-blue-100\">Tailored digital marketing plan for your business</p>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n              <h3 className=\"font-bold text-lg mb-2\">Quick Results</h3>\n              <p className=\"text-blue-100\">See improvements in your online presence within weeks</p>\n            </div>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Button \n              size=\"lg\" \n              className=\"bg-white text-purple-600 hover:bg-gray-100 font-semibold px-8 py-4 text-lg group\"\n            >\n              Request Free Strategy Session\n              <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n          </motion.div>\n\n          {/* Contact Options */}\n          <motion.div \n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mt-8\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.0 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Phone className=\"h-5 w-5\" />\n              <span>+977-9800000000</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <Mail className=\"h-5 w-5\" />\n              <span><EMAIL></span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-blue-100\">\n              <MessageCircle className=\"h-5 w-5\" />\n              <span>WhatsApp Available</span>\n            </div>\n          </motion.div>\n\n          {/* Urgency Element */}\n          <motion.div \n            className=\"mt-8 p-4 bg-yellow-400/20 rounded-lg border border-yellow-400/30\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n            viewport={{ once: true }}\n          >\n            <p className=\"text-yellow-100 font-medium\">\n              🎯 Limited Time: Get 20% off your first campaign when you book this month!\n            </p>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            className=\"flex flex-wrap justify-center items-center gap-8 mt-12 pt-8 border-t border-white/20\"\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.4 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">50+</div>\n              <div className=\"text-blue-200 text-sm\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">5+</div>\n              <div className=\"text-blue-200 text-sm\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">24/7</div>\n              <div className=\"text-blue-200 text-sm\">Support</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold\">100%</div>\n              <div className=\"text-blue-200 text-sm\">Satisfaction</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;gCACxB;gCAC+B;8CAC9B,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;;;;;;;sCAMjG,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCACxB;;;;;;sCAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAKjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;sCAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD", "debugId": null}}]}