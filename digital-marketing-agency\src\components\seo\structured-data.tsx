interface StructuredDataProps {
  type: 'organization' | 'localBusiness' | 'service' | 'review';
  data?: any;
}

export function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
    };

    switch (type) {
      case 'organization':
        return {
          ...baseData,
          "@type": "Organization",
          "name": "DigitalBoost",
          "alternateName": "Digital Marketing Agency Biratnagar",
          "url": "https://digitalboost.com.np",
          "logo": "https://digitalboost.com.np/logo.png",
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+977-9800000000",
            "contactType": "customer service",
            "areaServed": "NP",
            "availableLanguage": ["en", "ne"]
          },
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "Main Road, Biratnagar-15",
            "addressLocality": "Biratnagar",
            "addressRegion": "Morang",
            "postalCode": "56613",
            "addressCountry": "NP"
          },
          "sameAs": [
            "https://facebook.com/digitalboost",
            "https://instagram.com/digitalboost",
            "https://linkedin.com/company/digitalboost"
          ]
        };

      case 'localBusiness':
        return {
          ...baseData,
          "@type": "LocalBusiness",
          "name": "DigitalBoost",
          "image": "https://digitalboost.com.np/office.jpg",
          "telephone": "+977-9800000000",
          "email": "<EMAIL>",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "Main Road, Biratnagar-15",
            "addressLocality": "Biratnagar",
            "addressRegion": "Morang",
            "postalCode": "56613",
            "addressCountry": "NP"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": 26.4525,
            "longitude": 87.2718
          },
          "url": "https://digitalboost.com.np",
          "openingHoursSpecification": [
            {
              "@type": "OpeningHoursSpecification",
              "dayOfWeek": [
                "Sunday",
                "Monday", 
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday"
              ],
              "opens": "09:00",
              "closes": "18:00"
            },
            {
              "@type": "OpeningHoursSpecification",
              "dayOfWeek": "Saturday",
              "opens": "10:00",
              "closes": "16:00"
            }
          ],
          "priceRange": "NPR 25,000 - NPR 100,000",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "50"
          }
        };

      case 'service':
        return {
          ...baseData,
          "@type": "Service",
          "name": "Digital Marketing Services",
          "description": "Comprehensive digital marketing services including social media marketing, content creation, paid advertising, and brand development for businesses in Nepal.",
          "provider": {
            "@type": "Organization",
            "name": "DigitalBoost"
          },
          "areaServed": {
            "@type": "Country",
            "name": "Nepal"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Digital Marketing Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Social Media Marketing"
                }
              },
              {
                "@type": "Offer", 
                "itemOffered": {
                  "@type": "Service",
                  "name": "Content Creation"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service", 
                  "name": "Paid Advertising"
                }
              }
            ]
          }
        };

      case 'review':
        return {
          ...baseData,
          "@type": "Review",
          "itemReviewed": {
            "@type": "LocalBusiness",
            "name": "DigitalBoost"
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": data?.rating || "5",
            "bestRating": "5"
          },
          "name": data?.title || "Excellent Digital Marketing Services",
          "author": {
            "@type": "Person",
            "name": data?.author || "Client"
          },
          "reviewBody": data?.text || "Outstanding digital marketing services that delivered real results for our business.",
          "publisher": {
            "@type": "Organization",
            "name": "DigitalBoost"
          }
        };

      default:
        return baseData;
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData()),
      }}
    />
  );
}
