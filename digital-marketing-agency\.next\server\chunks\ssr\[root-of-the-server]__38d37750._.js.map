{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/about-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutHero = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-hero.tsx <module evaluation>\",\n    \"AboutHero\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,qEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/about-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutHero = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/about-hero.tsx\",\n    \"AboutHero\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/mission-vision.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MissionVision = registerClientReference(\n    function() { throw new Error(\"Attempted to call MissionVision() from the server but MissionVision is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/mission-vision.tsx <module evaluation>\",\n    \"MissionVision\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/mission-vision.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MissionVision = registerClientReference(\n    function() { throw new Error(\"Attempted to call MissionVision() from the server but MissionVision is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/mission-vision.tsx\",\n    \"MissionVision\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/team-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TeamSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TeamSection() from the server but TeamSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/team-section.tsx <module evaluation>\",\n    \"TeamSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,uEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/team-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const TeamSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TeamSection() from the server but TeamSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/team-section.tsx\",\n    \"TeamSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,mDACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/achievements-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AchievementsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call AchievementsSection() from the server but AchievementsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/achievements-section.tsx <module evaluation>\",\n    \"AchievementsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+EACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/about/achievements-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AchievementsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call AchievementsSection() from the server but AchievementsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/about/achievements-section.tsx\",\n    \"AchievementsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2DACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/cta-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CTASection = registerClientReference(\n    function() { throw new Error(\"Attempted to call CTASection() from the server but CTASection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/cta-section.tsx <module evaluation>\",\n    \"CTASection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,gEACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/components/cta-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CTASection = registerClientReference(\n    function() { throw new Error(\"Attempted to call CTASection() from the server but CTASection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/cta-section.tsx\",\n    \"CTASection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4CACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/agent/digital-marketing-agency/src/app/about/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from \"next\";\nimport { AboutHero } from \"@/components/about/about-hero\";\nimport { MissionVision } from \"@/components/about/mission-vision\";\nimport { TeamSection } from \"@/components/about/team-section\";\nimport { AchievementsSection } from \"@/components/about/achievements-section\";\nimport { CTASection } from \"@/components/cta-section\";\n\nexport const metadata: Metadata = {\n  title: \"About Us | Digital Marketing Agency Biratnagar Nepal\",\n  description: \"Learn about our mission, vision, and team at the leading digital marketing agency in Biratnagar, Nepal. Discover our story and achievements in social media marketing.\",\n  keywords: \"about digital marketing agency Biratnagar, team Nepal, mission vision, social media experts\",\n};\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen\">\n      <AboutHero />\n      <MissionVision />\n      <TeamSection />\n      <AchievementsSection />\n      <CTASection />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4IAAA,CAAA,YAAS;;;;;0BACV,8OAAC,gJAAA,CAAA,gBAAa;;;;;0BACd,8OAAC,8IAAA,CAAA,cAAW;;;;;0BACZ,8OAAC,sJAAA,CAAA,sBAAmB;;;;;0BACpB,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}