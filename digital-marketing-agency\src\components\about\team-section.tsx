"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { Linkedin, Twitter, Mail, User, Palette, BarChart3, Users } from "lucide-react";

const teamMembers = [
  {
    name: "<PERSON><PERSON>",
    role: "Founder & CEO",
    icon: User,
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",
    bio: "Digital marketing veteran with 8+ years of experience. Passionate about helping Nepalese businesses grow through innovative digital strategies.",
    expertise: ["Strategy", "Leadership", "Business Development"],
    social: {
      linkedin: "#",
      twitter: "#",
      email: "<EMAIL>"
    }
  },
  {
    name: "<PERSON><PERSON>",
    role: "Creative Director",
    icon: Palette,
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",
    bio: "Award-winning designer and content creator. Specializes in visual storytelling and brand development for social media platforms.",
    expertise: ["Design", "Content Creation", "Branding"],
    social: {
      linkedin: "#",
      twitter: "#",
      email: "<EMAIL>"
    }
  },
  {
    name: "Amit Rai",
    role: "Digital Marketing Manager",
    icon: BarChart3,
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",
    bio: "Google Ads certified professional with expertise in paid advertising and analytics. Drives ROI-focused campaigns for our clients.",
    expertise: ["Google Ads", "Facebook Ads", "Analytics"],
    social: {
      linkedin: "#",
      twitter: "#",
      email: "<EMAIL>"
    }
  },
  {
    name: "Sita Gurung",
    role: "Social Media Specialist",
    icon: Users,
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face",
    bio: "Social media expert with deep understanding of Nepalese market trends. Creates engaging content that resonates with local audiences.",
    expertise: ["Social Media", "Community Management", "Influencer Marketing"],
    social: {
      linkedin: "#",
      twitter: "#",
      email: "<EMAIL>"
    }
  }
];

export function TeamSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-green-600 border-green-600">
            Meet Our Team
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            The People Behind Your Success
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our diverse team of digital marketing experts brings together creativity, 
            technical expertise, and deep market knowledge to deliver exceptional results.
          </p>
        </motion.div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full card-hover border-0 shadow-lg group">
                <CardContent className="p-6 text-center">
                  {/* Avatar */}
                  <motion.div
                    className="relative w-20 h-20 rounded-full mb-4 mx-auto overflow-hidden"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Image
                      src={member.image}
                      alt={member.name}
                      fill
                      className="object-cover"
                    />
                  </motion.div>
                  
                  {/* Name & Role */}
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                  <p className="text-blue-600 font-medium mb-4">{member.role}</p>
                  
                  {/* Bio */}
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {member.bio}
                  </p>
                  
                  {/* Expertise Tags */}
                  <div className="flex flex-wrap gap-2 justify-center mb-6">
                    {member.expertise.map((skill) => (
                      <Badge key={skill} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                  
                  {/* Social Links */}
                  <div className="flex justify-center space-x-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <a 
                      href={member.social.linkedin} 
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <Linkedin size={18} />
                    </a>
                    <a 
                      href={member.social.twitter} 
                      className="text-gray-400 hover:text-blue-400 transition-colors"
                    >
                      <Twitter size={18} />
                    </a>
                    <a 
                      href={`mailto:${member.social.email}`} 
                      className="text-gray-400 hover:text-green-600 transition-colors"
                    >
                      <Mail size={18} />
                    </a>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Team Culture */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Culture</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-3">
              <div className="text-4xl">🚀</div>
              <h4 className="font-semibold text-gray-900">Innovation First</h4>
              <p className="text-gray-600 text-sm">We embrace new technologies and creative approaches to solve complex marketing challenges.</p>
            </div>
            <div className="space-y-3">
              <div className="text-4xl">🤝</div>
              <h4 className="font-semibold text-gray-900">Collaboration</h4>
              <p className="text-gray-600 text-sm">We believe in the power of teamwork and open communication to achieve extraordinary results.</p>
            </div>
            <div className="space-y-3">
              <div className="text-4xl">📈</div>
              <h4 className="font-semibold text-gray-900">Continuous Learning</h4>
              <p className="text-gray-600 text-sm">We invest in our team's growth and stay updated with the latest industry trends and best practices.</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
