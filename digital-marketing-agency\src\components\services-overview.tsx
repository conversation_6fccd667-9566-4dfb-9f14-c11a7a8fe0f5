"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import {
  Share2,
  Users,
  Camera,
  Target,
  TrendingUp,
  Megaphone
} from "lucide-react";

const services = [
  {
    icon: Share2,
    title: "Social Media Marketing",
    description: "Strategic social media campaigns across Facebook, Instagram, TikTok, and LinkedIn to boost your brand presence and engagement.",
    features: ["Content Strategy", "Community Management", "Social Analytics", "Brand Monitoring"],
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Users,
    title: "Influencer Promotions",
    description: "Connect with top influencers in Nepal to amplify your brand message and reach targeted audiences authentically.",
    features: ["Influencer Matching", "Campaign Management", "Performance Tracking", "ROI Analysis"],
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Camera,
    title: "Content Creation",
    description: "Professional photos, engaging reels, stunning graphics, and compelling copy that tells your brand story effectively.",
    features: ["Photography", "Video Production", "Graphic Design", "Copywriting"],
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Target,
    title: "Paid Advertising",
    description: "Data-driven advertising campaigns on Meta, TikTok, and Google to maximize your ROI and drive conversions.",
    features: ["Meta Ads", "Google Ads", "TikTok Ads", "Campaign Optimization"],
    color: "from-orange-500 to-red-500"
  }
];

export function ServicesOverview() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-blue-600 border-blue-600">
            Our Core Services
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Comprehensive Digital Solutions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From strategy to execution, we provide end-to-end digital marketing services 
            that drive real results for businesses across Nepal.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full card-hover border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${service.color}`}>
                      <service.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        {service.title}
                      </h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {service.description}
                      </p>
                      <div className="space-y-2">
                        <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {service.features.map((feature) => (
                            <div key={feature} className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              <span className="text-sm text-gray-600">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div 
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <div className="flex items-center justify-center mb-4">
              <TrendingUp className="h-8 w-8 mr-3" />
              <h3 className="text-2xl font-bold">Ready to Grow Your Business?</h3>
            </div>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Let's discuss how our digital marketing expertise can help your business 
              reach new heights in the Nepalese market.
            </p>
            <motion.button 
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Get Free Consultation
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
