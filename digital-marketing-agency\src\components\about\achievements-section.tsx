"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Award, TrendingUp, Users, Star, Calendar, Target, Rocket, Zap } from "lucide-react";

const achievements = [
  {
    icon: Award,
    title: "Best Digital Marketing Agency 2023",
    description: "Recognized by Nepal Business Awards for outstanding contribution to digital marketing industry",
    year: "2023",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: TrendingUp,
    title: "300% Average ROI",
    description: "Consistently delivered exceptional returns on investment for our clients across all campaigns",
    year: "2024",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Users,
    title: "50+ Happy Clients",
    description: "Built lasting partnerships with businesses across Nepal, from startups to established enterprises",
    year: "2024",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Star,
    title: "4.9/5 Client Rating",
    description: "Maintained exceptional client satisfaction scores through dedicated service and results",
    year: "2024",
    color: "from-purple-500 to-pink-500"
  }
];

const milestones = [
  {
    year: "2019",
    title: "Company Founded",
    description: "Started DigitalBoost with a vision to transform Nepalese businesses"
  },
  {
    year: "2020",
    title: "First 10 Clients",
    description: "Successfully onboarded our first batch of clients during challenging times"
  },
  {
    year: "2021",
    title: "Team Expansion",
    description: "Grew our team to 8 specialists covering all aspects of digital marketing"
  },
  {
    year: "2022",
    title: "100+ Projects",
    description: "Reached the milestone of completing 100 successful marketing campaigns"
  },
  {
    year: "2023",
    title: "Industry Recognition",
    description: "Received multiple awards and recognition from industry bodies"
  },
  {
    year: "2024",
    title: "Market Leadership",
    description: "Established as one of the leading digital marketing agencies in Nepal"
  }
];

export function AchievementsSection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 text-orange-600 border-orange-600">
            Our Achievements
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Milestones & Recognition
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our journey has been marked by significant achievements and recognition 
            that reflect our commitment to excellence and client success.
          </p>
        </motion.div>

        {/* Achievements Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {achievements.map((achievement, index) => (
            <motion.div
              key={achievement.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full card-hover border-0 shadow-lg text-center">
                <CardContent className="p-6">
                  <div className={`w-16 h-16 bg-gradient-to-r ${achievement.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <achievement.icon className="h-8 w-8 text-white" />
                  </div>
                  
                  <Badge variant="secondary" className="mb-3">
                    {achievement.year}
                  </Badge>
                  
                  <h3 className="text-lg font-bold text-gray-900 mb-3">
                    {achievement.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {achievement.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">Our Journey</h3>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>
            
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card className="border-0 shadow-lg">
                      <CardContent className="p-6">
                        <div className="flex items-center mb-3">
                          <Calendar className="h-5 w-5 text-blue-600 mr-2" />
                          <Badge variant="outline" className="text-blue-600 border-blue-600">
                            {milestone.year}
                          </Badge>
                        </div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">
                          {milestone.title}
                        </h4>
                        <p className="text-gray-600">
                          {milestone.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Timeline Dot */}
                  <div className="w-4 h-4 bg-white border-4 border-blue-500 rounded-full z-10"></div>
                  
                  <div className="w-1/2"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center"
        >
          <h3 className="text-2xl font-bold mb-8">By the Numbers</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <div className="text-3xl font-bold mb-2">200+</div>
              <div className="text-blue-100">Campaigns Launched</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">5M+</div>
              <div className="text-blue-100">People Reached</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">98%</div>
              <div className="text-blue-100">Client Retention</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">24/7</div>
              <div className="text-blue-100">Support Available</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
