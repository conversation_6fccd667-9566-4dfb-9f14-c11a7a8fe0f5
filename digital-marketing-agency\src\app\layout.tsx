import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { StructuredData } from "@/components/seo/structured-data";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Digital Marketing Agency Biratnagar | Social Media Marketing Nepal",
  description: "Leading digital marketing agency in Biratnagar, Nepal specializing in social media marketing, content creation, Facebook ads, Instagram marketing, and online branding for businesses.",
  keywords: "digital marketing agency Biratnagar, Facebook ads Nepal, Instagram marketing Biratnagar, best social media agency Nepal, content creation Nepal",
  authors: [{ name: "Digital Marketing Agency Biratnagar" }],
  openGraph: {
    title: "Digital Marketing Agency Biratnagar | Social Media Marketing Nepal",
    description: "Leading digital marketing agency in Biratnagar, Nepal specializing in social media marketing, content creation, and online branding.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Digital Marketing Agency Biratnagar | Social Media Marketing Nepal",
    description: "Leading digital marketing agency in Biratnagar, Nepal specializing in social media marketing, content creation, and online branding.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <StructuredData type="organization" />
        <StructuredData type="localBusiness" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <div className="min-h-screen flex flex-col">
          <Navigation />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
